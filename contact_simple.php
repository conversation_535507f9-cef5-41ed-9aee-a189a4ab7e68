<?php
include_once("Database/connect.php");

if(isset($_POST['submit'])) {
    $name = mysqli_real_escape_string($con, $_POST['name']);
    $email = mysqli_real_escape_string($con, $_POST['email']);
    $phone = mysqli_real_escape_string($con, $_POST['phone']);
    $event_type = mysqli_real_escape_string($con, $_POST['event_type']);
    $message = mysqli_real_escape_string($con, $_POST['message']);
    
    // Store in database
    $query = "INSERT INTO contact_messages (name, email, phone, event_type, message, created_at) 
              VALUES ('$name', '$email', '$phone', '$event_type', '$message', NOW())";
    
    if(mysqli_query($con, $query)) {
        // Simple email using PHP mail()
        $to = "<EMAIL>";
        $subject = "New Contact Form Submission - FirmAnt Events";
        $headers = "From: FirmAnt Events <<EMAIL>>\r\n";
        $headers .= "Reply-To: $email\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $email_body = "
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> $name</p>
        <p><strong>Email:</strong> $email</p>
        <p><strong>Phone:</strong> $phone</p>
        <p><strong>Event Type:</strong> $event_type</p>
        <p><strong>Message:</strong> $message</p>
        <p><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</p>
        ";
        mail($to, $subject, $email_body, $headers);
        header('Location: contact_simple.php?success=1');
        exit();
    } else {
        $error_message = "❌ Sorry, there was an error. Please try again.";
    }
}
?>