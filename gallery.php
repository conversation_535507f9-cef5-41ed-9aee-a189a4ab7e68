<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery | FirmAnt Event Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Lightbox -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark: #2c3e50;
            --light: #ecf0f1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
        }

        /* Animated Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 4px 30px rgba(0,0,0,0.15);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        /* Gallery Hero Section */
        .gallery-hero {
            background: var(--primary);
            min-height: 70vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .gallery-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            animation: fadeInUp 1s ease;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.3s both;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Gallery Filters */
        .gallery-filters {
            background: #f8f9fa;
            padding: 40px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .filter-btn {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
            border-radius: 50px;
            padding: 15px 35px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary);
            color: white;
            border-color: transparent;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        /* Gallery Grid */
        .gallery-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .gallery-item {
            margin-bottom: 30px;
        }

        .gallery-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
        }

        .gallery-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.2);
        }

        .gallery-image {
            position: relative;
            overflow: hidden;
        }

        .gallery-card img {
            width: 100%;
            height: 280px;
            object-fit: cover;
            transition: all 0.4s ease;
        }

        .gallery-card:hover img {
            transform: scale(1.1);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
            opacity: 0;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .gallery-card:hover .image-overlay {
            opacity: 1;
        }

        .overlay-content {
            text-align: center;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .gallery-card:hover .overlay-content {
            transform: translateY(0);
        }

        .gallery-card .card-body {
            padding: 25px;
        }

        .gallery-card .card-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .price-tag {
            color: #667eea;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .btn-book {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
        }

        .btn-book:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
            color: white;
        }

        .btn-cart {
            background: var(--success);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-cart:hover {
            background: linear-gradient(135deg, #38f9d7 0%, #43e97b 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
            color: white;
        }

        /* Stats Section */
        .stats-section {
            background: var(--accent);
            color: white;
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 30px 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        .footer-brand {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            margin-right: 10px;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
        }

        .social-links a:hover {
            background: var(--primary);
            transform: translateY(-3px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .filter-btn {
                padding: 12px 25px;
                margin: 5px;
            }
            
            .gallery-card img {
                height: 220px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:48px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link active" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Gallery Hero Section -->
    <section class="gallery-hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-images me-3"></i>Our <span style="background: var(--secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Gallery</span>
                </h1>
                <p class="hero-subtitle">Discover moments from our beautiful events and celebrations</p>
            </div>
        </div>
    </section>

    <!-- Gallery Filters -->
    <section class="gallery-filters">
        <div class="container">
            <div class="text-center">
                <a href="gallery.php" class="filter-btn active">
                    <i class="fas fa-heart me-2"></i>Wedding
                </a>
                <a href="bday_gal.php" class="filter-btn">
                    <i class="fas fa-birthday-cake me-2"></i>Birthday Party
                </a>
                <a href="anni_gal.php" class="filter-btn">
                    <i class="fas fa-ring me-2"></i>Anniversary
                </a>
                <a href="other_gal.php" class="filter-btn">
                    <i class="fas fa-star me-2"></i>Entertainment
                </a>
            </div>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="gallery-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Wedding Gallery</h2>
                <p class="lead text-muted">Beautiful moments from our wedding celebrations</p>
            </div>
            
            <div class="row g-4">
                <?php
                    include_once("Database/connect.php");
                    $qry="select * from wedding ORDER BY id DESC";
                    $res=mysqli_query($con,$qry) or die("can't fetch data");
                    $delay = 100;
                    while($row=mysqli_fetch_array($res)){
                ?>
                <div class="col-lg-4 col-md-6 gallery-item" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                    <div class="gallery-card">
                        <div class="gallery-image">
                            <a href="images/<?php echo $row['img']; ?>" data-lightbox="wedding-gallery" data-title="<?php echo isset($row['nm']) ? $row['nm'] : 'Wedding Event'; ?>">
                                <img src="images/<?php echo $row['img']; ?>" alt="Wedding Event">
                                <div class="image-overlay">
                                    <div class="overlay-content">
                                        <i class="fas fa-search-plus fa-3x mb-3"></i>
                                        <h5>Click to view</h5>
                                        <p>Explore this beautiful moment</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title"><?php echo isset($row['nm']) ? $row['nm'] : 'Wedding Package'; ?></h5>
                            <?php if(isset($row['price'])): ?>
                                <p class="price-tag mb-3">Starting from $<?php echo number_format($row['price']); ?></p>
                            <?php endif; ?>
                            <div class="d-grid gap-2">
                                <button onclick="addToCart(<?php echo $row['id']; ?>, 'wedding')" class="btn btn-cart">
                                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                </button>
                                <a href="book_wed.php?id=<?php echo $row['id']; ?>" class="btn btn-book">
                                    <i class="fas fa-calendar-plus me-2"></i>Book Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php 
                    $delay += 100;
                    if($delay > 800) $delay = 100;
                    } 
                ?>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="100">
                    <span class="stat-number">500+</span>
                    <p class="stat-label">Weddings Planned</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="200">
                    <span class="stat-number">1000+</span>
                    <p class="stat-label">Happy Clients</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="300">
                    <span class="stat-number">50+</span>
                    <p class="stat-label">Team Members</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="400">
                    <span class="stat-number">5+</span>
                    <p class="stat-label">Years Experience</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="footer-brand mb-3">
                        <i class="fas fa-calendar-star me-2"></i>FirmAnt Events
                    </div>
                    <p class="text-muted">Creating unforgettable moments and extraordinary experiences for every special occasion.</p>
                    <div class="social-links mt-4">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Services</h5>
                    <ul class="list-unstyled">
                        <li><a href="gallery.php" class="text-muted">Weddings</a></li>
                        <li><a href="bday_gal.php" class="text-muted">Birthday Parties</a></li>
                        <li><a href="anni_gal.php" class="text-muted">Anniversaries</a></li>
                        <li><a href="other_gal.php" class="text-muted">Corporate Events</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted">Home</a></li>
                        <li><a href="gallery.php" class="text-muted">Gallery</a></li>
                        <li><a href="projects.php" class="text-muted">Projects</a></li>
                        <li><a href="contact.php" class="text-muted">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">Contact Info</h5>
                    <p class="text-muted"><i class="fas fa-phone me-2"></i>+91 90333 36811</p>
                    <p class="text-muted"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt me-2"></i>Your Location Here</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Lightbox configuration
        lightbox.option({
            'resizeDuration': 200,
            'wrapAround': true,
            'albumLabel': "Image %1 of %2"
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Add to cart function
        function addToCart(itemId, itemType, quantity = 1) {
            const formData = new FormData();
            formData.append('item_id', itemId);
            formData.append('item_type', itemType);
            formData.append('quantity', quantity);

            fetch('add_to_cart.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update cart count in navbar
                    const cartBadge = document.querySelector('.badge');
                    if (cartBadge) {
                        cartBadge.textContent = data.cart_count;
                        if (data.cart_count > 0) {
                            cartBadge.style.display = 'inline';
                        }
                    } else if (data.cart_count > 0) {
                        // Create badge if it doesn't exist
                        const cartIcon = document.querySelector('.fa-shopping-cart').parentElement;
                        const badge = document.createElement('span');
                        badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                        badge.style.fontSize = '0.7rem';
                        badge.textContent = data.cart_count;
                        cartIcon.appendChild(badge);
                    }

                    // Show success message
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('An error occurred while adding item to cart', 'error');
            });
        }

        function showMessage(message, type) {
            // Create and show a toast message
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;
            document.body.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>

