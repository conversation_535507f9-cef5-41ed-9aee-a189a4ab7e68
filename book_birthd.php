<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Birthday Party | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .booking-hero {
            background: var(--secondary);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .booking-section {
            padding: 80px 0;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .booking-form {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
            background: white;
        }

        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-select:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
            background: white;
        }

        .btn-book {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-book:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
            color: white;
        }

        .package-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .package-price {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .package-features {
            list-style: none;
            padding: 0;
        }

        .package-features li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .package-features li:last-child {
            border-bottom: none;
        }

        .package-features i {
            color: #28a745;
            margin-right: 10px;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .booking-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-star me-2"></i>FirmAnt
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                    <li class="nav-item"><a class="nav-link" href="registration.php">Register</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="booking-hero">
        <div class="container">
            <h1 class="hero-title">Book Birthday Party</h1>
            <p class="hero-subtitle">Create magical memories for your special day</p>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking-section">
        <div class="container">
            <div class="row">
                <!-- Booking Form -->
                <div class="col-lg-8">
                    <div class="booking-card" data-aos="fade-up">
                        <div class="booking-form">
                            <h3 class="mb-4">Birthday Party Details</h3>
                            
                            <?php
                            include_once("Database/connect.php");
                            
                            if(isset($_POST['submit'])) {
                                $name = $_POST['name'];
                                $email = $_POST['email'];
                                $phone = $_POST['phone'];
                                $event_date = $_POST['event_date'];
                                $guests = $_POST['guests'];
                                $venue = $_POST['venue'];
                                $package_id = isset($_GET['id']) ? $_GET['id'] : '';
                                $message = $_POST['message'];
                                $age_group = $_POST['age_group'];
                                $theme = $_POST['theme'];
                                
                                $query = "INSERT INTO birthday_bookings (name, email, phone, event_date, guests, venue, package_id, message, age_group, theme, booking_date) 
                                         VALUES ('$name', '$email', '$phone', '$event_date', '$guests', '$venue', '$package_id', '$message', '$age_group', '$theme', NOW())";
                                
                                if(mysqli_query($con, $query)) {
                                    echo "<div class='alert alert-success'>Booking request submitted successfully! We'll contact you soon.</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>Error submitting booking. Please try again.</div>";
                                }
                            }
                            ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Event Date</label>
                                            <input type="date" class="form-control" name="event_date" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Age Group</label>
                                            <select class="form-select" name="age_group" required>
                                                <option value="">Select age group</option>
                                                <option value="1-5">1-5 years (Toddler)</option>
                                                <option value="6-12">6-12 years (Kids)</option>
                                                <option value="13-17">13-17 years (Teen)</option>
                                                <option value="18+">18+ years (Adult)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Number of Guests</label>
                                            <select class="form-select" name="guests" required>
                                                <option value="">Select guest count</option>
                                                <option value="10-25">10-25 guests</option>
                                                <option value="25-50">25-50 guests</option>
                                                <option value="50-100">50-100 guests</option>
                                                <option value="100+">100+ guests</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Party Theme</label>
                                            <select class="form-select" name="theme" required>
                                                <option value="">Select theme</option>
                                                <option value="superhero">Superhero Adventure</option>
                                                <option value="princess">Princess Fantasy</option>
                                                <option value="space">Space Explorer</option>
                                                <option value="cartoon">Cartoon Characters</option>
                                                <option value="sports">Sports Theme</option>
                                                <option value="elegant">Elegant Adult</option>
                                                <option value="custom">Custom Theme</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Preferred Venue</label>
                                            <select class="form-select" name="venue" required>
                                                <option value="">Select venue type</option>
                                                <option value="home">Home/Private</option>
                                                <option value="restaurant">Restaurant</option>
                                                <option value="hotel">Hotel</option>
                                                <option value="outdoor">Outdoor/Park</option>
                                                <option value="hall">Event Hall</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Special Requirements</label>
                                    <textarea class="form-control" name="message" rows="4" placeholder="Tell us about your vision, special requirements, dietary restrictions, or any specific requests..."></textarea>
                                </div>
                                
                                <button type="submit" name="submit" class="btn-book">
                                    <i class="fas fa-birthday-cake me-2"></i>Submit Booking Request
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Package Info -->
                <div class="col-lg-4">
                    <div class="package-card" data-aos="fade-up" data-aos-delay="200">
                        <?php
                        if(isset($_GET['id'])) {
                            $package_id = $_GET['id'];
                            $query = "SELECT * FROM birthday WHERE id = $package_id";
                            $result = mysqli_query($con, $query);
                            if($package = mysqli_fetch_array($result)) {
                        ?>
                        <div class="text-center mb-4">
                            <img src="images/<?php echo $package['img']; ?>" alt="Package" class="img-fluid rounded-3 mb-3">
                            <h4><?php echo $package['nm']; ?></h4>
                            <div class="package-price">$<?php echo $package['price']; ?></div>
                        </div>
                        <?php
                            }
                        }
                        ?>
                        
                        <h5 class="mb-3">Birthday Package Includes:</h5>
                        <ul class="package-features">
                            <li><i class="fas fa-check"></i> Themed decorations</li>
                            <li><i class="fas fa-check"></i> Custom birthday cake</li>
                            <li><i class="fas fa-check"></i> Party games & activities</li>
                            <li><i class="fas fa-check"></i> Entertainment/Clown</li>
                            <li><i class="fas fa-check"></i> Party favors & balloons</li>
                            <li><i class="fas fa-check"></i> Photography service</li>
                            <li><i class="fas fa-check"></i> Event coordination</li>
                            <li><i class="fas fa-check"></i> Setup & cleanup</li>
                        </ul>
                        
                        <div class="mt-4 p-3 bg-light rounded-3">
                            <h6><i class="fas fa-info-circle me-2"></i>Booking Information</h6>
                            <small class="text-muted">
                                • 50% deposit required to confirm booking<br>
                                • Free consultation included<br>
                                • Cancellation policy applies<br>
                                • Custom themes available<br>
                                • Age-appropriate entertainment
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <h4 class="mb-3"><i class="fas fa-calendar-star me-2"></i>FirmAnt Events</h4>
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Birthday Party | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .booking-hero {
            background: var(--secondary);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .booking-section {
            padding: 80px 0;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .booking-form {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
            background: white;
        }

        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-select:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
            background: white;
        }

        .btn-book {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-book:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
            color: white;
        }

        .package-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .package-price {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .package-features {
            list-style: none;
            padding: 0;
        }

        .package-features li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .package-features li:last-child {
            border-bottom: none;
        }

        .package-features i {
            color: #28a745;
            margin-right: 10px;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .booking-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-star me-2"></i>FirmAnt
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                    <li class="nav-item"><a class="nav-link" href="registration.php">Register</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="booking-hero">
        <div class="container">
            <h1 class="hero-title">Book Birthday Party</h1>
            <p class="hero-subtitle">Create magical memories for your special day</p>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking-section">
        <div class="container">
            <div class="row">
                <!-- Booking Form -->
                <div class="col-lg-8">
                    <div class="booking-card" data-aos="fade-up">
                        <div class="booking-form">
                            <h3 class="mb-4">Birthday Party Details</h3>
                            
                            <?php
                            include_once("Database/connect.php");
                            
                            if(isset($_POST['submit'])) {
                                $name = $_POST['name'];
                                $email = $_POST['email'];
                                $phone = $_POST['phone'];
                                $event_date = $_POST['event_date'];
                                $guests = $_POST['guests'];
                                $venue = $_POST['venue'];
                                $package_id = isset($_GET['id']) ? $_GET['id'] : '';
                                $message = $_POST['message'];
                                $age_group = $_POST['age_group'];
                                $theme = $_POST['theme'];
                                
                                $query = "INSERT INTO birthday_bookings (name, email, phone, event_date, guests, venue, package_id, message, age_group, theme, booking_date) 
                                         VALUES ('$name', '$email', '$phone', '$event_date', '$guests', '$venue', '$package_id', '$message', '$age_group', '$theme', NOW())";
                                
                                if(mysqli_query($con, $query)) {
                                    echo "<div class='alert alert-success'>Booking request submitted successfully! We'll contact you soon.</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>Error submitting booking. Please try again.</div>";
                                }
                            }
                            ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Event Date</label>
                                            <input type="date" class="form-control" name="event_date" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Age Group</label>
                                            <select class="form-select" name="age_group" required>
                                                <option value="">Select age group</option>
                                                <option value="1-5">1-5 years (Toddler)</option>
                                                <option value="6-12">6-12 years (Kids)</option>
                                                <option value="13-17">13-17 years (Teen)</option>
                                                <option value="18+">18+ years (Adult)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Number of Guests</label>
                                            <select class="form-select" name="guests" required>
                                                <option value="">Select guest count</option>
                                                <option value="10-25">10-25 guests</option>
                                                <option value="25-50">25-50 guests</option>
                                                <option value="50-100">50-100 guests</option>
                                                <option value="100+">100+ guests</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Party Theme</label>
                                            <select class="form-select" name="theme" required>
                                                <option value="">Select theme</option>
                                                <option value="superhero">Superhero Adventure</option>
                                                <option value="princess">Princess Fantasy</option>
                                                <option value="space">Space Explorer</option>
                                                <option value="cartoon">Cartoon Characters</option>
                                                <option value="sports">Sports Theme</option>
                                                <option value="elegant">Elegant Adult</option>
                                                <option value="custom">Custom Theme</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Preferred Venue</label>
                                            <select class="form-select" name="venue" required>
                                                <option value="">Select venue type</option>
                                                <option value="home">Home/Private</option>
                                                <option value="restaurant">Restaurant</option>
                                                <option value="hotel">Hotel</option>
                                                <option value="outdoor">Outdoor/Park</option>
                                                <option value="hall">Event Hall</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Special Requirements</label>
                                    <textarea class="form-control" name="message" rows="4" placeholder="Tell us about your vision, special requirements, dietary restrictions, or any specific requests..."></textarea>
                                </div>
                                
                                <button type="submit" name="submit" class="btn-book">
                                    <i class="fas fa-birthday-cake me-2"></i>Submit Booking Request
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Package Info -->
                <div class="col-lg-4">
                    <div class="package-card" data-aos="fade-up" data-aos-delay="200">
                        <?php
                        if(isset($_GET['id'])) {
                            $package_id = $_GET['id'];
                            $query = "SELECT * FROM birthday WHERE id = $package_id";
                            $result = mysqli_query($con, $query);
                            if($package = mysqli_fetch_array($result)) {
                        ?>
                        <div class="text-center mb-4">
                            <img src="images/<?php echo $package['img']; ?>" alt="Package" class="img-fluid rounded-3 mb-3">
                            <h4><?php echo $package['nm']; ?></h4>
                            <div class="package-price">$<?php echo $package['price']; ?></div>
                        </div>
                        <?php
                            }
                        }
                        ?>
                        
                        <h5 class="mb-3">Birthday Package Includes:</h5>
                        <ul class="package-features">
                            <li><i class="fas fa-check"></i> Themed decorations</li>
                            <li><i class="fas fa-check"></i> Custom birthday cake</li>
                            <li><i class="fas fa-check"></i> Party games & activities</li>
                            <li><i class="fas fa-check"></i> Entertainment/Clown</li>
                            <li><i class="fas fa-check"></i> Party favors & balloons</li>
                            <li><i class="fas fa-check"></i> Photography service</li>
                            <li><i class="fas fa-check"></i> Event coordination</li>
                            <li><i class="fas fa-check"></i> Setup & cleanup</li>
                        </ul>
                        
                        <div class="mt-4 p-3 bg-light rounded-3">
                            <h6><i class="fas fa-info-circle me-2"></i>Booking Information</h6>
                            <small class="text-muted">
                                • 50% deposit required to confirm booking<br>
                                • Free consultation included<br>
                                • Cancellation policy applies<br>
                                • Custom themes available<br>
                                • Age-appropriate entertainment
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <h4 class="mb-3"><i class="fas fa-calendar-star me-2"></i>FirmAnt Events</h4>
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

