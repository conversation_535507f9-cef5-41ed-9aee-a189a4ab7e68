<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and order number is provided
if (!isset($_SESSION['uid']) || !isset($_GET['order'])) {
    header('Location: index.php');
    exit();
}

include_once("Database/connect.php");

$order_number = $_GET['order'];
$user_id = $_SESSION['uid'];

// Get order details
$order_query = "SELECT o.*, u.name as user_name, u.email as user_email 
                FROM orders o 
                JOIN user u ON o.user_id = u.id 
                WHERE o.order_number = ? AND o.user_id = ?";
$order_stmt = mysqli_prepare($con, $order_query);
mysqli_stmt_bind_param($order_stmt, "si", $order_number, $user_id);
mysqli_stmt_execute($order_stmt);
$order_result = mysqli_stmt_get_result($order_stmt);

if (mysqli_num_rows($order_result) == 0) {
    header('Location: index.php');
    exit();
}

$order = mysqli_fetch_assoc($order_result);

// Get order items
$items_query = "SELECT * FROM order_items WHERE order_id = ?";
$items_stmt = mysqli_prepare($con, $items_query);
mysqli_stmt_bind_param($items_stmt, "i", $order['id']);
mysqli_stmt_execute($items_stmt);
$items_result = mysqli_stmt_get_result($items_stmt);

$order_items = array();
while ($item = mysqli_fetch_assoc($items_result)) {
    $order_items[] = $item;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --dark: #1a1a2e;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .success-hero {
            background: var(--success);
            color: white;
            padding: 120px 0 60px;
            text-align: center;
        }

        .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .order-section {
            padding: 60px 0;
        }

        .order-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .order-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .order-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .order-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 20px;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            color: #666;
            font-size: 0.9rem;
            text-transform: capitalize;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .summary-row:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 1.2rem;
            color: #28a745;
        }

        .btn-primary-custom {
            background: var(--primary);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 10px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .info-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> 
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">Home</a>
                <a class="nav-link" href="gallery.php">Gallery</a>
                <a class="nav-link" href="contact.php">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Success Hero -->
    <section class="success-hero">
        <div class="container">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="hero-title">Order Confirmed!</h1>
            <p class="lead">Thank you for your order. We'll be in touch soon to finalize the details.</p>
        </div>
    </section>

    <!-- Order Details Section -->
    <section class="order-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="order-card">
                        <div class="order-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Order Details</h4>
                                    <div class="order-number">Order #<?php echo $order['order_number']; ?></div>
                                    <p class="text-muted">Placed on <?php echo date('F j, Y', strtotime($order['created_at'])); ?></p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <span class="badge bg-success fs-6">Order Confirmed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="order-items mb-4">
                            <h5 class="mb-3">Items Ordered</h5>
                            <?php foreach ($order_items as $item): ?>
                                <div class="order-item">
                                    <img src="images/<?php echo $item['item_image']; ?>" alt="<?php echo $item['item_name']; ?>" class="item-image">
                                    <div class="item-details">
                                        <div class="item-name"><?php echo $item['item_name']; ?></div>
                                        <div class="item-type"><?php echo $item['item_type']; ?> package</div>
                                        <div class="text-muted">Quantity: <?php echo $item['quantity']; ?> × $<?php echo number_format($item['unit_price'], 2); ?></div>
                                    </div>
                                    <div class="item-total">
                                        <strong>$<?php echo number_format($item['total_price'], 2); ?></strong>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Order Summary -->
                        <div class="order-summary">
                            <h5 class="mb-3">Order Summary</h5>
                            <div class="summary-row">
                                <span>Subtotal</span>
                                <span>$<?php echo number_format($order['total_amount'], 2); ?></span>
                            </div>
                            <div class="summary-row">
                                <span>Tax</span>
                                <span>$<?php echo number_format($order['tax_amount'], 2); ?></span>
                            </div>
                            <div class="summary-row">
                                <span>Total</span>
                                <span>$<?php echo number_format($order['final_amount'], 2); ?></span>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="info-section">
                            <div class="info-title">What happens next?</div>
                            <ul class="mb-0">
                                <li>Our team will review your order and contact you within 24 hours</li>
                                <li>We'll discuss event details, timeline, and any customizations</li>
                                <li>A detailed proposal and contract will be provided</li>
                                <li>Payment schedule and final arrangements will be confirmed</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center mt-4">
                            <a href="index.php" class="btn-primary-custom">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                            <a href="gallery.php" class="btn-primary-custom">
                                <i class="fas fa-images me-2"></i>View Gallery
                            </a>
                            <a href="contact.php" class="btn-primary-custom">
                                <i class="fas fa-phone me-2"></i>Contact Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
