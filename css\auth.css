/* --- Improved Split-screen authentication layout --- */
body.auth-bg {
  background: #232136;
  min-height: 100vh;
  font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
  color: #fff;
}
.auth-container {
  display: flex;
  min-height: 100vh;
}
.auth-left {
  flex: 1.1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  min-width: 340px;
  background: #232136 url('../images/banner.jpg') center center/cover no-repeat;
  box-shadow: 2px 0 24px 0 rgba(0,0,0,0.10);
}
.auth-left::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(35,33,54,0.82);
  z-index: 1;
}
.auth-left-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 48px 32px 32px 32px;
  min-height: 100vh;
}
.auth-left .logo {
  width: 180px;
  max-width: 80%;
  margin: 0 auto 32px auto;
  display: block;
}
.auth-left .back-btn {
  align-self: flex-end;
  background: rgba(255,255,255,0.10);
  color: #fff;
  border: none;
  border-radius: 24px;
  padding: 10px 26px;
  font-size: 1.08rem;
  margin-bottom: 24px;
  margin-top: 8px;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.auth-left .back-btn:hover {
  background: #8b5cf6;
  color: #fff;
}
.auth-left .slogan {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 24px;
  text-align: center;
  color: #fff;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 12px rgba(0,0,0,0.18);
  margin-bottom: 32px;
}

.auth-right {
  flex: 1;
  background: #232136;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 48px 32px;
  min-width: 340px;
}
.auth-form-box {
  width: 100%;
  max-width: 420px;
  background: rgba(45,42,69,0.98);
  border-radius: 22px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.18);
  padding: 48px 36px 36px 36px;
  margin: 32px 0;
}
.auth-form-box h2 {
  font-size: 2.4rem;
  font-weight: 800;
  margin-bottom: 8px;
  color: #fff;
  letter-spacing: 0.5px;
}
.auth-form-box .subtext {
  color: #bcb8d7;
  font-size: 1.08rem;
  margin-bottom: 24px;
}
.auth-form-box .form-label {
  color: #bcb8d7;
  font-size: 1rem;
  margin-bottom: 4px;
}
.auth-form-box .form-control {
  background: #232136;
  border: 1.5px solid #393552;
  color: #fff;
  border-radius: 12px;
  font-size: 1.13rem;
  padding: 16px 18px;
  margin-bottom: 18px;
  transition: border 0.2s;
  font-weight: 500;
}
.auth-form-box .form-control:focus {
  border-color: #8b5cf6;
  background: #232136;
  color: #fff;
  box-shadow: 0 0 0 2px #8b5cf633;
}
.auth-form-box .form-control::placeholder {
  color: #bcb8d7 !important;
  opacity: 1;
  font-weight: 400;
  font-size: 1.08rem;
}
.auth-form-box .form-check-label {
  color: #bcb8d7;
  font-size: 1.01rem;
}
.auth-form-box .form-check-input:checked {
  background-color: #8b5cf6;
  border-color: #8b5cf6;
}
.auth-form-box .btn-primary {
  background: #8b5cf6;
  border: none;
  border-radius: 12px;
  font-size: 1.18rem;
  font-weight: 700;
  padding: 15px 0;
  margin-top: 8px;
  margin-bottom: 16px;
  transition: background 0.2s;
  box-shadow: 0 2px 12px #8b5cf644;
}
.auth-form-box .btn-primary:hover {
  background: #7c3aed;
}
.auth-form-box .form-text {
  color: #bcb8d7;
  font-size: 0.97rem;
}
.auth-form-box .social-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 18px 0 12px 0;
}
.auth-form-box .social-divider span {
  flex: 1;
  height: 1px;
  background: #393552;
}
.auth-form-box .social-divider p {
  margin: 0 12px;
  color: #bcb8d7;
  font-size: 1.01rem;
}
.auth-form-box .social-btns {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}
.auth-form-box .social-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #232136;
  border: 1.5px solid #393552;
  color: #fff;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 10px 0;
  transition: border 0.2s, background 0.2s;
  cursor: pointer;
}
.auth-form-box .social-btn:hover {
  border-color: #8b5cf6;
  background: #2d2a45;
}
.auth-form-box .social-btn i {
  font-size: 1.3rem;
  margin-right: 8px;
}
.auth-form-box .form-link {
  color: #8b5cf6;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
}
.auth-form-box .form-link:hover {
  text-decoration: underline;
}
.auth-form-box .show-password {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #bcb8d7;
  cursor: pointer;
  font-size: 1.2rem;
}
@media (max-width: 991px) {
  .auth-container {
    flex-direction: column;
  }
  .auth-left, .auth-right {
    min-width: 0;
    width: 100%;
    padding: 32px 12px;
  }
  .auth-left-content {
    padding: 32px 12px 12px 12px;
    min-height: 220px;
  }
  .auth-left .logo {
    width: 120px;
    margin-bottom: 16px;
  }
  .auth-left .slogan {
    font-size: 1.1rem;
    margin-bottom: 16px;
  }
  .auth-form-box {
    padding: 32px 10px 24px 10px;
    margin: 18px 0;
  }
} 