<?php
// Test Cart Functionality
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include_once("cart_functions.php");

echo "<h2>Cart Functionality Test</h2>";

// Test 1: Check if cart manager is working
echo "<h3>Test 1: Cart Manager Initialization</h3>";
if (isset($cartManager)) {
    echo "✅ Cart Manager initialized successfully<br>";
} else {
    echo "❌ Cart Manager failed to initialize<br>";
}

// Test 2: Check database connection
echo "<h3>Test 2: Database Connection</h3>";
if (isset($con) && $con) {
    echo "✅ Database connection successful<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// Test 3: Check session
echo "<h3>Test 3: Session Status</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ Session is not active<br>";
}

// Test 4: Check user login status
echo "<h3>Test 4: User Login Status</h3>";
if (isset($_SESSION['uid'])) {
    echo "✅ User is logged in (ID: " . $_SESSION['uid'] . ")<br>";
    echo "User name: " . (isset($_SESSION['uname']) ? $_SESSION['uname'] : 'Not set') . "<br>";
} else {
    echo "ℹ️ User is not logged in (guest mode)<br>";
}

// Test 5: Check cart count
echo "<h3>Test 5: Cart Count</h3>";
$cart_count = $cartManager->getCartCount();
echo "Current cart count: " . $cart_count . "<br>";

// Test 6: Display current cart contents
echo "<h3>Test 6: Current Cart Contents</h3>";
if (isset($_SESSION['uid'])) {
    $cart_items = $cartManager->getUserCartItems($_SESSION['uid']);
    if (!empty($cart_items)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Item ID</th><th>Type</th><th>Name</th><th>Quantity</th><th>Price</th></tr>";
        foreach ($cart_items as $item) {
            echo "<tr>";
            echo "<td>" . $item['item_id'] . "</td>";
            echo "<td>" . $item['item_type'] . "</td>";
            echo "<td>" . $item['item_name'] . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>$" . number_format($item['price'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Cart is empty<br>";
    }
} else {
    if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Item ID</th><th>Type</th><th>Name</th><th>Quantity</th><th>Price</th></tr>";
        foreach ($_SESSION['cart'] as $key => $item) {
            echo "<tr>";
            echo "<td>" . $item['id'] . "</td>";
            echo "<td>" . $item['type'] . "</td>";
            echo "<td>" . $item['name'] . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>$" . number_format($item['price'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Session cart is empty<br>";
    }
}

// Test 7: Test adding an item to cart (if wedding items exist)
echo "<h3>Test 7: Test Add to Cart</h3>";
$test_query = "SELECT * FROM wedding LIMIT 1";
$test_result = mysqli_query($con, $test_query);
if ($test_row = mysqli_fetch_assoc($test_result)) {
    echo "Found test item: " . $test_row['nm'] . " (ID: " . $test_row['id'] . ")<br>";
    
    if (isset($_GET['test_add'])) {
        $success = $cartManager->addToCart($test_row['id'], 'wedding', 1);
        if ($success) {
            echo "✅ Successfully added item to cart<br>";
            echo "<a href='test_cart.php'>Refresh to see updated cart</a><br>";
        } else {
            echo "❌ Failed to add item to cart<br>";
        }
    } else {
        echo "<a href='test_cart.php?test_add=1'>Click here to test adding this item to cart</a><br>";
    }
} else {
    echo "No wedding items found in database for testing<br>";
}

// Test 8: Database tables check
echo "<h3>Test 8: Database Tables Check</h3>";
$tables_to_check = ['user', 'cart', 'orders', 'order_items'];
foreach ($tables_to_check as $table) {
    $check_query = "SHOW TABLES LIKE '$table'";
    $check_result = mysqli_query($con, $check_query);
    if (mysqli_num_rows($check_result) > 0) {
        echo "✅ Table '$table' exists<br>";
    } else {
        echo "❌ Table '$table' does not exist<br>";
    }
}

echo "<hr>";
echo "<h3>Navigation</h3>";
echo "<a href='index.php'>← Back to Home</a> | ";
echo "<a href='cart.php'>View Cart</a> | ";
echo "<a href='gallery.php'>Gallery</a>";
if (!isset($_SESSION['uid'])) {
    echo " | <a href='login.php'>Login</a>";
} else {
    echo " | <a href='logout.php'>Logout</a>";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}
h2 {
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}
h3 {
    color: #555;
    margin-top: 20px;
}
table {
    margin: 10px 0;
    background: white;
}
th {
    background: #667eea;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
a {
    color: #667eea;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
