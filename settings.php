<?php
session_start();
if (!isset($_SESSION['uid'])) {
    header('Location: login.php');
    exit();
}
include_once 'Database/connect.php';
$user_id = $_SESSION['uid'];
$query = "SELECT * FROM registration WHERE id = ?";
$stmt = $con->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? $user['nm']);
    $phone = trim($_POST['phone'] ?? $user['mo']);
    // Optionally handle password change
    if (!empty($_POST['password'])) {
        $password = $_POST['password'];
        $update = $con->prepare("UPDATE registration SET nm=?, mo=?, pswd=? WHERE id=?");
        $update->bind_param('sssi', $name, $phone, $password, $user_id);
    } else {
        $update = $con->prepare("UPDATE registration SET nm=?, mo=? WHERE id=?");
        $update->bind_param('ssi', $name, $phone, $user_id);
    }
    if ($update->execute()) {
        header('Location: settings.php?success=1');
        exit();
    } else {
        $error = 'Failed to update profile.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings | FirmAnt</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Poppins', sans-serif; }
        .settings-card {
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            padding: 40px 30px;
            max-width: 500px;
            margin: 40px auto;
        }
        .settings-title { font-size: 2rem; font-weight: 700; color: #667eea; margin-bottom: 30px; }
        .form-label { font-weight: 600; color: #333; }
        .btn-save { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; border: none; border-radius: 30px; padding: 10px 30px; font-weight: 600; transition: 0.2s; }
        .btn-save:hover { background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); }
    </style>
</head>
<body>
    <div class="container">
        <div class="settings-card">
            <div class="settings-title text-center">Profile & Settings</div>
            <?php if($success): ?>
                <div class="alert alert-success"> <?php echo $success; ?> </div>
            <?php endif; ?>
            <?php if($error): ?>
                <div class="alert alert-danger"> <?php echo $error; ?> </div>
            <?php endif; ?>
            <form method="post">
                <div class="mb-3">
                    <label class="form-label">Full Name</label>
                    <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['nm']); ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Phone</label>
                    <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($user['mo']); ?>">
                </div>
                <div class="mb-3">
                    <label class="form-label">Change Password</label>
                    <input type="password" name="password" class="form-control" placeholder="Leave blank to keep current password">
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="notification_email" id="notification_email" <?php if (isset($user['notification_email']) && $user['notification_email']) echo 'checked'; ?>>
                    <label class="form-check-label" for="notification_email">Email Notifications</label>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="notification_sms" id="notification_sms" <?php if (isset($user['notification_sms']) && $user['notification_sms']) echo 'checked'; ?>>
                    <label class="form-check-label" for="notification_sms">SMS Alerts</label>
                </div>
                <button type="submit" class="btn btn-save w-100">Save Changes</button>
            </form>
            <div class="text-center mt-3">
                <a href="profile.php" class="text-decoration-none">&larr; Back to Profile</a>
            </div>
        </div>
    </div>
</body>
</html>