# PHPMailer Installation Guide

## Method 1: Using Composer (Recommended)
```bash
composer require phpmailer/phpmailer
```

## Method 2: Manual Installation
1. Download PHPMailer from: https://github.com/PHPMailer/PHPMailer
2. Extract to your project folder
3. Include the files manually in contact.php

## Gmail App Password Setup
1. Go to Google Account settings
2. Enable 2-Factor Authentication
3. Go to Security > App passwords
4. Generate app password for "Mail"
5. Use this password in SMTP_PASSWORD

## Alternative SMTP Providers
- **Gmail**: smtp.gmail.com:587
- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **SendGrid**: smtp.sendgrid.net:587