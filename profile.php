

<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'Database/connect.php';
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}
$user_id = $_SESSION['user_id'];
$msg = '';

// Handle profile picture upload
if (isset($_POST['upload_pic'])) {
    if (isset($_FILES['profile_pic']) && $_FILES['profile_pic']['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES['profile_pic']['name'], PATHINFO_EXTENSION));
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($ext, $allowed)) {
            $target = 'images/profile_' . $user_id . '.' . $ext;
            if (move_uploaded_file($_FILES['profile_pic']['tmp_name'], $target)) {
                // Check if profile_pic column exists
                $col_check = mysqli_query($con, "SHOW COLUMNS FROM registration LIKE 'profile_pic'");
                if (mysqli_num_rows($col_check) > 0) {
                    mysqli_query($con, "UPDATE registration SET profile_pic='$target' WHERE id='$user_id'");
                    $msg = '<div class="alert alert-success">Profile picture updated!</div>';
                } else {
                    // No column, but still update the displayed image for this session
                    $_SESSION['profile_pic_uploaded'] = $target;
                    $msg = '<div class="alert alert-warning">Profile picture column missing in database. Displaying uploaded image for this session only.</div>';
                }
                // Force reload to show new image
                echo "<script>location.reload();</script>";
                exit();
            } else {
                $msg = '<div class="alert alert-danger">Failed to upload image.</div>';
            }
        } else {
            $msg = '<div class="alert alert-warning">Invalid file type.</div>';
        }
    }
}

// Handle password change
if (isset($_POST['change_password'])) {
    $old = $_POST['old_password'] ?? '';
    $new = $_POST['new_password'] ?? '';
    $confirm = $_POST['confirm_password'] ?? '';
    $res = mysqli_query($con, "SELECT password FROM registration WHERE id='$user_id'");
    $row = mysqli_fetch_assoc($res);
    if ($row && password_verify($old, $row['password'])) {
        if ($new === $confirm && strlen($new) >= 6) {
            $hash = password_hash($new, PASSWORD_DEFAULT);
            mysqli_query($con, "UPDATE registration SET password='$hash' WHERE id='$user_id'");
            $msg = '<div class="alert alert-success">Password changed successfully!</div>';
        } else {
            $msg = '<div class="alert alert-warning">Passwords do not match or too short.</div>';
        }
    } else {
        $msg = '<div class="alert alert-danger">Old password incorrect.</div>';
    }
}

// Handle contact info update
if (isset($_POST['update_contact'])) {
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    mysqli_query($con, "UPDATE registration SET email='$email', mo='$phone' WHERE id='$user_id'");
    $msg = '<div class="alert alert-success">Contact info updated!</div>';
}

// Handle account deletion
if (isset($_POST['delete_account'])) {
    mysqli_query($con, "DELETE FROM registration WHERE id='$user_id'");
    session_destroy();
    header('Location: index.php');
    exit();
}

// Fetch user info
$res = mysqli_query($con, "SELECT * FROM registration WHERE id='$user_id'");
$user = mysqli_fetch_assoc($res);
// Use default avatar if column missing
$profile_pic = 'images/default-avatar.png';
$col_check = mysqli_query($con, "SHOW COLUMNS FROM registration LIKE 'profile_pic'");
if (mysqli_num_rows($col_check) > 0 && !empty($user['profile_pic'])) {
    $profile_pic = $user['profile_pic'];
} elseif (!empty($_SESSION['profile_pic_uploaded'])) {
    $profile_pic = $_SESSION['profile_pic_uploaded'];
}

// Fetch event history
$events = mysqli_query($con, "SELECT * FROM general_bookings WHERE email='" . mysqli_real_escape_string($con, $user['email']) . "' ORDER BY booking_date DESC LIMIT 10");

// Wishlist table does not exist, skip wishlist functionality
// $wishlist = false;

// Fetch feedback
$feedback = mysqli_query($con, "SELECT * FROM feedback WHERE email='" . mysqli_real_escape_string($con, $user['email']) . "' LIMIT 5");

// Notifications table does not exist, skip notifications functionality
// $notifications = false;

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - FirmAnt</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div class="min-h-screen bg-gray-50 py-10 px-2">
        <div class="max-w-4xl mx-auto mb-4">
            <a href="index.php" class="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold shadow transition text-decoration-none">
                <i class="fa fa-arrow-left"></i> Home
            </a>
        </div>
        <div class="max-w-4xl mx-auto bg-white rounded-2xl shadow-lg p-6 md:p-10 flex flex-col md:flex-row gap-8">
            <!-- Profile Card -->
            <div class="flex flex-col items-center md:w-1/3 w-full">
                <div class="w-28 h-28 rounded-full overflow-hidden border-4 border-blue-200 mb-3">
                    <img src="<?php echo $profile_pic; ?>" alt="Profile Picture" class="object-cover w-full h-full">
                </div>
                <!-- Change Picture Button triggers modal -->
                <button type="button" onclick="document.getElementById('picModal').classList.remove('hidden')" class="w-full py-2 px-4 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition mb-2">Change Picture</button>
                <div class="text-lg font-bold text-gray-800 mt-2"><?php echo htmlspecialchars($user['nm'] ?? ''); ?></div>
                <div class="text-gray-500 text-sm"><?php echo htmlspecialchars($user['mo'] ?? ''); ?></div>
            </div>
            <!-- Info and Security -->
            <div class="flex-1 flex flex-col gap-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-1">User profile</h2>
                    <p class="text-gray-500 text-sm mb-4">Manage your details, view your tier status and change your password.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 rounded-xl p-4 flex flex-col gap-2">
                            <label class="text-xs text-gray-500">First name</label>
                            <input type="text" class="input input-bordered w-full border border-gray-200 rounded-lg px-3 py-2" value="<?php echo htmlspecialchars(preg_split('/\s+/', $user['nm'])[0] ?? ''); ?>" readonly>
                        </div>
                        <div class="bg-gray-50 rounded-xl p-4 flex flex-col gap-2">
                            <label class="text-xs text-gray-500">Last name</label>
                            <input type="text" class="input input-bordered w-full border border-gray-200 rounded-lg px-3 py-2" value="<?php echo htmlspecialchars((($parts = preg_split('/\s+/', $user['nm'])) && count($parts) > 1) ? $parts[1] : ''); ?>" readonly>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Security</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="flex flex-col gap-1">
                            <label class="text-xs text-gray-500">Email</label>
                            <input type="email" class="input input-bordered w-full border border-gray-200 rounded-lg px-3 py-2" placeholder="e.g. <EMAIL>" value="" readonly>
                        </div>
                        <div class="flex flex-col gap-1">
                            <label class="text-xs text-gray-500">Password</label>
                            <input type="password" class="input input-bordered w-full border border-gray-200 rounded-lg px-3 py-2" value="********" readonly>
                        </div>
                        <div class="flex flex-col gap-1">
                            <label class="text-xs text-gray-500">Phone number</label>
                            <input type="text" class="input input-bordered w-full border border-gray-200 rounded-lg px-3 py-2" placeholder="e.g. +237 6XX XXX XXX" value="" readonly>
                        </div>
        <!-- Profile Picture Modal -->
        <div id="picModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
                <h4 class="font-bold mb-3">Change Profile Picture</h4>
                <form method="post" enctype="multipart/form-data">
                    <div class="mb-2">
                        <input type="file" name="profile_pic" accept="image/*" class="form-control" required>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <button type="submit" name="upload_pic" class="btn btn-success flex-1">Upload</button>
                        <button type="button" onclick="document.getElementById('picModal').classList.add('hidden')" class="btn btn-outline-secondary flex-1">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
                    </div>
                    <div class="flex flex-col md:flex-row gap-3 mt-4">
                        <form method="post" class="flex-1">
                            <input type="hidden" name="change_password" value="1">
                            <button type="button" onclick="document.getElementById('passwordModal').classList.remove('hidden')" class="w-full border border-green-400 text-green-700 font-semibold py-2 rounded-lg hover:bg-green-50 transition">Change password</button>
                        </form>
                        <form method="post" class="flex-1">
                            <input type="hidden" name="change_phone" value="1">
                            <button type="button" onclick="document.getElementById('phoneModal').classList.remove('hidden')" class="w-full border border-blue-400 text-blue-700 font-semibold py-2 rounded-lg hover:bg-blue-50 transition">Change phone number</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Password Modal -->
        <div id="passwordModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
                <h4 class="font-bold mb-3">Change Password</h4>
                <form method="post">
                    <div class="mb-2">
                        <label class="text-xs text-gray-500">Old Password</label>
                        <input type="password" name="old_password" class="form-control" required>
                    </div>
                    <div class="mb-2">
                        <label class="text-xs text-gray-500">New Password</label>
                        <input type="password" name="new_password" class="form-control" required>
                    </div>
                    <div class="mb-2">
                        <label class="text-xs text-gray-500">Confirm Password</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <button type="submit" name="change_password" class="btn btn-success flex-1">Save</button>
                        <button type="button" onclick="document.getElementById('passwordModal').classList.add('hidden')" class="btn btn-outline-secondary flex-1">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
        <!-- Phone Modal -->
        <div id="phoneModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
                <h4 class="font-bold mb-3">Change Phone Number</h4>
                <form method="post">
                    <div class="mb-2">
                        <label class="text-xs text-gray-500">New Phone Number</label>
                        <input type="text" name="phone" class="form-control" required>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <button type="submit" name="update_contact" class="btn btn-success flex-1">Save</button>
                        <button type="button" onclick="document.getElementById('phoneModal').classList.add('hidden')" class="btn btn-outline-secondary flex-1">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        // Hide modals on outside click
        window.onclick = function(event) {
            if(event.target === document.getElementById('passwordModal')) {
                document.getElementById('passwordModal').classList.add('hidden');
            }
            if(event.target === document.getElementById('phoneModal')) {
                document.getElementById('phoneModal').classList.add('hidden');
            }
            if(event.target === document.getElementById('picModal')) {
                document.getElementById('picModal').classList.add('hidden');
            }
        }
    </script>
    <script>
        function showTab(tab) {
            const tabs = ['info', 'events', 'wishlist', 'feedback', 'settings'];
            tabs.forEach(function(t) {
                document.getElementById('tab-' + t).classList.remove('active');
                document.getElementById(t).classList.remove('show', 'active');
            });
            document.getElementById('tab-' + tab).classList.add('active');
            document.getElementById(tab).classList.add('show', 'active');
        }
    </script>
</body>
</html>