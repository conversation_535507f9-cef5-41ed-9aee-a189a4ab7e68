<?php
// Start session at the very beginning
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .cart-hero {
            background: var(--primary);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .cart-section {
            padding: 80px 0;
        }

        .cart-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .cart-item {
            padding: 25px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .cart-item:hover {
            background: #f8f9fa;
        }

        .item-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 15px;
        }

        .item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .item-price {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 8px;
        }

        .remove-btn {
            background: var(--secondary);
            border: none;
            border-radius: 10px;
            padding: 8px 15px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .remove-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
        }

        .cart-summary {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            position: sticky;
            top: 100px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .summary-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .checkout-btn {
            background: var(--success);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .checkout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(67, 233, 123, 0.4);
            color: white;
        }

        .continue-shopping {
            background: transparent;
            border: 2px solid #667eea;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            color: #667eea;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
        }

        .continue-shopping:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
        }

        .item-image {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }

        .quantity-controls-wrapper {
            display: flex;
            align-items: center;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            margin: 0;
        }

        .quantity-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quantity-btn:hover {
            background: #e9ecef;
        }

        .quantity-input {
            width: 50px;
            height: 30px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            outline: none;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .item-total {
            min-width: 80px;
            text-align: center;
        }

        .item-price {
            margin-bottom: 0.5rem;
            color: #28a745;
            font-weight: 600;
        }

        .cart-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .cart-item:hover {
            background-color: #f8f9fa;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .empty-cart {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-cart-icon {
            font-size: 5rem;
            color: #e9ecef;
            margin-bottom: 20px;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .cart-item {
                padding: 20px 15px;
            }
            
            .cart-summary {
                position: static;
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Items - Centered with left margin -->
                <ul class="navbar-nav mx-auto align-items-center" style="margin-left: 3rem;">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                </ul>

                <!-- User Actions Group - Right aligned -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item me-3">
                        <?php
                        include_once("cart_functions.php");
                        $cart_count = $cartManager->getCartCount();
                        ?>
                        <a class="nav-link position-relative" href="cart.php" title="View Cart">
                            <i class="fas fa-shopping-cart fa-lg"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;"> <?php echo $cart_count; ?> </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <?php if (isset($_SESSION['uid'])): ?>
                            <a class="nav-link d-flex align-items-center text-danger" href="logout.php?redirect=login">
                                <i class="fas fa-sign-out-alt fa-lg me-1"></i> Sign Out
                            </a>
                        <?php else: ?>
                            <a class="nav-link d-flex align-items-center" href="login.php">
                                <i class="fas fa-sign-in-alt fa-lg me-1"></i> Login
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="cart-hero">
        <div class="container">
            <h1 class="hero-title">Shopping Cart</h1>
            <p class="hero-subtitle">Review your selected event packages</p>
        </div>
    </section>

    <!-- Cart Section -->
    <section class="cart-section">
        <div class="container">
            <?php
            include_once("cart_functions.php");

            // Handle cart actions
            if (isset($_POST['action'])) {
                if ($_POST['action'] == 'remove') {
                    $item_id = intval($_POST['item_id']);
                    $item_type = $_POST['item_type'];
                    $cartManager->removeFromCart($item_id, $item_type);
                }

                if ($_POST['action'] == 'update') {
                    $item_id = intval($_POST['item_id']);
                    $item_type = $_POST['item_type'];
                    $quantity = max(1, intval($_POST['quantity']));
                    $cartManager->updateCartQuantity($item_id, $item_type, $quantity);
                }
            }

            // Get cart items
            $cart_items = array();
            $total = 0;

            if (isset($_SESSION['uid'])) {
                // Logged in user - get from database
                $user_cart_items = $cartManager->getUserCartItems($_SESSION['uid']);
                foreach ($user_cart_items as $item) {
                    $cart_key = $item['item_type'] . '_' . $item['item_id'];
                    $cart_items[$cart_key] = array(
                        'id' => $item['item_id'],
                        'type' => $item['item_type'],
                        'name' => $item['item_name'],
                        'image' => $item['item_image'],
                        'price' => $item['price'],
                        'quantity' => $item['quantity'],
                        'description' => "Premium " . ucfirst($item['item_type']) . " Package"
                    );
                    $total += $item['price'] * $item['quantity'];
                }
            } else {
                // Guest user - get from session
                if (isset($_SESSION['cart'])) {
                    $cart_items = $_SESSION['cart'];
                    foreach ($cart_items as $item) {
                        $total += $item['price'] * $item['quantity'];
                    }
                }
            }
            ?>
            
            <div class="row">
                <div class="col-lg-8">
                    <div class="cart-card" data-aos="fade-up">
                        <?php if (empty($cart_items)): ?>
                            <div class="empty-cart">
                                <div class="empty-cart-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h3>Your cart is empty</h3>
                                <p class="text-muted mb-4">Looks like you haven't added any event packages yet.</p>
                                <a href="gallery.php" class="continue-shopping">
                                    <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="cart-items">
                                <?php foreach ($cart_items as $item_key => $item): ?>
                                    <div class="cart-item">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                <img src="images/<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>" class="item-image">
                                            </div>
                                            <div class="col-md-5">
                                                <h5 class="item-title"><?php echo $item['name']; ?></h5>
                                                <p class="item-description text-muted"><?php echo $item['description']; ?></p>
                                                <p class="item-price"><strong>$<?php echo number_format($item['price'], 2); ?></strong></p>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <div class="quantity-controls-wrapper">
                                                        <form method="post" action="cart.php" class="quantity-controls" data-item-id="<?php echo $item['id']; ?>" data-item-type="<?php echo $item['type']; ?>">
                                                            <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                                            <input type="hidden" name="item_type" value="<?php echo $item['type']; ?>">
                                                            <input type="hidden" name="action" value="update">
                                                            <button type="button" class="quantity-btn" onclick="changeQuantity('<?php echo $item['id']; ?>', '<?php echo $item['type']; ?>', -1)"><i class="fas fa-minus"></i></button>
                                                            <input type="number" class="quantity-input" name="quantity" data-item-id="<?php echo $item['id']; ?>" data-item-type="<?php echo $item['type']; ?>" value="<?php echo $item['quantity']; ?>" min="1" onchange="submitQuantityForm('<?php echo $item['id']; ?>', '<?php echo $item['type']; ?>')">
                                                            <button type="button" class="quantity-btn" onclick="changeQuantity('<?php echo $item['id']; ?>', '<?php echo $item['type']; ?>', 1)"><i class="fas fa-plus"></i></button>
                                                        </form>
                                                    </div>
                                                    <div class="item-total">
                                                        <strong>$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></strong>
                                                    </div>
                                                    <form method="post" action="cart.php" style="display: inline;">
                                                        <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                                        <input type="hidden" name="item_type" value="<?php echo $item['type']; ?>">
                                                        <input type="hidden" name="action" value="remove">
                                                        <button type="submit" class="remove-btn" onclick="return confirm('Are you sure you want to remove this item?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="cart-summary" data-aos="fade-up">
                        <h4 class="summary-title">Cart Summary</h4>
                        <div class="summary-row">
                            <span>Subtotal</span>
                            <span><?php echo number_format($total, 2); ?> USD</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax (10%)</span>
                            <span><?php echo number_format($total * 0.1, 2); ?> USD</span>
                        </div>
                        <div class="summary-row">
                            <span>Total</span>
                            <span><?php echo number_format($total * 1.1, 2); ?> USD</span>
                        </div>
                        <a href="checkout.php" class="checkout-btn">Proceed to Checkout</a>
                        <a href="gallery.php" class="continue-shopping">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        function changeQuantity(itemId, itemType, change) {
            const input = document.querySelector(`input[name='quantity'][data-item-id='${itemId}'][data-item-type='${itemType}']`);
            let newValue = parseInt(input.value) + change;
            if (newValue < 1) newValue = 1;
            input.value = newValue;
            submitQuantityForm(itemId, itemType);
        }

        function submitQuantityForm(itemId, itemType) {
            const form = document.querySelector(`form.quantity-controls[data-item-id='${itemId}'][data-item-type='${itemType}']`);
            if (form) form.submit();
        }

        // Add to cart function for AJAX requests
        function addToCart(itemId, itemType, quantity = 1) {
            const formData = new FormData();
            formData.append('item_id', itemId);
            formData.append('item_type', itemType);
            formData.append('quantity', quantity);

            fetch('add_to_cart.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update cart count in navbar
                    const cartBadge = document.querySelector('.badge');
                    if (cartBadge) {
                        cartBadge.textContent = data.cart_count;
                        if (data.cart_count > 0) {
                            cartBadge.style.display = 'inline';
                        }
                    }

                    // Show success message
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('An error occurred while adding item to cart', 'error');
            });
        }

        function showMessage(message, type) {
            // Create and show a toast message
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;
            document.body.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init();
    </script>
</body>
</html>

