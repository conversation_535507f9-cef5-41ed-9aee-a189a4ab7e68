<?php
// Enhanced PDF Receipt Download for FirmAnt Event Management System
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and order number is provided
if (!isset($_SESSION['uid']) || !isset($_GET['order'])) {
    header('Location: index.php');
    exit();
}

include_once("Database/connect.php");

$order_number = $_GET['order'];
$user_id = $_SESSION['uid'];

// Get order details
$order_query = "SELECT o.*, u.name as user_name, u.email as user_email, u.phone as user_phone 
                FROM orders o 
                JOIN user u ON o.user_id = u.id 
                WHERE o.order_number = ? AND o.user_id = ?";
$order_stmt = mysqli_prepare($con, $order_query);
mysqli_stmt_bind_param($order_stmt, "si", $order_number, $user_id);
mysqli_stmt_execute($order_stmt);
$order_result = mysqli_stmt_get_result($order_stmt);

if (mysqli_num_rows($order_result) == 0) {
    header('Location: index.php');
    exit();
}

$order = mysqli_fetch_assoc($order_result);

// Get order items
$items_query = "SELECT * FROM order_items WHERE order_id = ?";
$items_stmt = mysqli_prepare($con, $items_query);
mysqli_stmt_bind_param($items_stmt, "i", $order['id']);
mysqli_stmt_execute($items_stmt);
$items_result = mysqli_stmt_get_result($items_stmt);

$order_items = array();
while ($item = mysqli_fetch_assoc($items_result)) {
    $order_items[] = $item;
}

// Set headers for PDF download
header('Content-Type: text/html; charset=utf-8');

// Generate the receipt HTML
$receipt_html = generateReceiptHTML($order, $order_items);

// Output the HTML (which can be saved as PDF by the browser)
echo $receipt_html;

function generateReceiptHTML($order, $order_items) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Receipt - Order #<?php echo $order['order_number']; ?></title>
        <style>
            @page {
                size: A4;
                margin: 20mm;
            }
            
            @media print {
                body { margin: 0; }
                .no-print { display: none !important; }
                .receipt-container { box-shadow: none; margin: 0; }
            }
            
            * {
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Arial', sans-serif;
                margin: 0;
                padding: 0;
                background: white;
                color: #333;
                line-height: 1.6;
            }
            
            .receipt-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 30px;
                background: white;
            }
            
            .receipt-header {
                text-align: center;
                border-bottom: 3px solid #667eea;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            
            .company-logo {
                max-height: 60px;
                margin-bottom: 15px;
            }
            
            .company-name {
                font-size: 32px;
                font-weight: bold;
                color: #667eea;
                margin: 10px 0 5px 0;
            }
            
            .company-tagline {
                color: #666;
                font-style: italic;
                font-size: 14px;
            }
            
            .receipt-title {
                font-size: 28px;
                font-weight: bold;
                color: #333;
                margin: 25px 0;
                text-align: center;
                text-transform: uppercase;
                letter-spacing: 2px;
            }
            
            .order-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 40px;
                margin-bottom: 30px;
            }
            
            .info-section h3 {
                color: #667eea;
                font-size: 18px;
                margin-bottom: 15px;
                border-bottom: 2px solid #eee;
                padding-bottom: 8px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            
            .info-section p {
                margin: 8px 0;
                color: #333;
                font-size: 14px;
            }
            
            .info-section strong {
                color: #667eea;
                font-weight: 600;
            }
            
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 30px 0;
                font-size: 14px;
            }
            
            .items-table th {
                background: #667eea;
                color: white;
                padding: 15px 10px;
                text-align: left;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
                font-size: 12px;
            }
            
            .items-table td {
                padding: 12px 10px;
                border-bottom: 1px solid #eee;
                vertical-align: top;
            }
            
            .items-table tr:nth-child(even) {
                background: #f9f9f9;
            }
            
            .items-table tr:hover {
                background: #f0f4ff;
            }
            
            .total-section {
                margin-top: 30px;
                padding: 25px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 5px solid #667eea;
            }
            
            .total-row {
                display: flex;
                justify-content: space-between;
                margin: 10px 0;
                font-size: 16px;
                padding: 5px 0;
            }
            
            .total-row.final {
                font-size: 22px;
                font-weight: bold;
                color: #667eea;
                border-top: 2px solid #667eea;
                padding-top: 15px;
                margin-top: 20px;
            }
            
            .status-badge {
                display: inline-block;
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 11px;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            
            .status-confirmed {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .status-pending {
                background: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            .footer {
                margin-top: 40px;
                text-align: center;
                color: #666;
                font-size: 12px;
                border-top: 2px solid #eee;
                padding-top: 20px;
            }
            
            .footer p {
                margin: 5px 0;
            }
            
            .footer .company-contact {
                font-weight: bold;
                color: #667eea;
            }
            
            .address-section {
                grid-column: 1 / -1;
                margin-top: 20px;
            }
            
            .notes-section {
                margin-top: 30px;
                padding: 20px;
                background: #fff9c4;
                border-left: 4px solid #ffc107;
                border-radius: 4px;
            }
            
            .watermark {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 100px;
                color: rgba(102, 126, 234, 0.05);
                z-index: -1;
                font-weight: bold;
                pointer-events: none;
            }
        </style>
        <script>
            // Auto-print when page loads
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            }
        </script>
    </head>
    <body>
        <div class="watermark">FIRMANT</div>
        
        <div class="receipt-container">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <div class="company-name">FIRMANT</div>
                <div class="company-name" style="font-size: 20px; margin-top: -5px;">EVENT MANAGEMENT</div>
                <div class="company-tagline">Creating Memorable Moments Since 2024</div>
            </div>
            
            <div class="receipt-title">Payment Receipt</div>
            
            <!-- Order Information -->
            <div class="order-info">
                <div class="info-section">
                    <h3>Order Details</h3>
                    <p><strong>Order Number:</strong> <?php echo $order['order_number']; ?></p>
                    <p><strong>Order Date:</strong> <?php echo date('F j, Y', strtotime($order['created_at'])); ?></p>
                    <p><strong>Order Time:</strong> <?php echo date('g:i A', strtotime($order['created_at'])); ?></p>
                    <p><strong>Status:</strong> 
                        <span class="status-badge status-<?php echo $order['status']; ?>">
                            <?php echo ucfirst($order['status']); ?>
                        </span>
                    </p>
                    <p><strong>Payment Status:</strong> 
                        <span class="status-badge status-<?php echo $order['payment_status']; ?>">
                            <?php echo ucfirst($order['payment_status']); ?>
                        </span>
                    </p>
                </div>
                
                <div class="info-section">
                    <h3>Customer Information</h3>
                    <p><strong>Name:</strong> <?php echo htmlspecialchars($order['user_name']); ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($order['user_email']); ?></p>
                    <?php if (!empty($order['user_phone'])): ?>
                        <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['user_phone']); ?></p>
                    <?php endif; ?>
                    <p><strong>Customer ID:</strong> #<?php echo str_pad($order['user_id'], 6, '0', STR_PAD_LEFT); ?></p>
                </div>
                
                <?php if (!empty($order['shipping_address'])): ?>
                <div class="info-section address-section">
                    <h3>Delivery Address</h3>
                    <p><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Order Items -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 40%;">Item Description</th>
                        <th style="width: 15%;">Category</th>
                        <th style="width: 10%;">Qty</th>
                        <th style="width: 15%;">Unit Price</th>
                        <th style="width: 20%;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order_items as $item): ?>
                    <tr>
                        <td>
                            <strong><?php echo htmlspecialchars($item['item_name']); ?></strong>
                            <br><small style="color: #666;">Premium Event Package</small>
                        </td>
                        <td><?php echo ucfirst($item['item_type']); ?></td>
                        <td style="text-align: center;"><?php echo $item['quantity']; ?></td>
                        <td style="text-align: right;">$<?php echo number_format($item['unit_price'], 2); ?></td>
                        <td style="text-align: right;"><strong>$<?php echo number_format($item['total_price'], 2); ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Total Section -->
            <div class="total-section">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span>$<?php echo number_format($order['total_amount'], 2); ?></span>
                </div>
                <div class="total-row">
                    <span>Tax (10%):</span>
                    <span>$<?php echo number_format($order['tax_amount'], 2); ?></span>
                </div>
                <div class="total-row">
                    <span>Shipping & Handling:</span>
                    <span>$0.00</span>
                </div>
                <div class="total-row final">
                    <span>TOTAL AMOUNT PAID:</span>
                    <span>$<?php echo number_format($order['final_amount'], 2); ?></span>
                </div>
            </div>
            
            <?php if (!empty($order['notes'])): ?>
            <div class="notes-section">
                <h3 style="margin-top: 0; color: #856404;">Special Instructions</h3>
                <p style="margin-bottom: 0;"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
            </div>
            <?php endif; ?>
            
            <!-- Footer -->
            <div class="footer">
                <p class="company-contact"><strong>FIRMANT EVENT MANAGEMENT</strong></p>
                <p>📧 <EMAIL> | 📞 +1 (555) 123-4567 | 🌐 www.firmant.com</p>
                <p>123 Event Street, Celebration City, EC 12345</p>
                <hr style="margin: 15px 0; border: none; border-top: 1px solid #ddd;">
                <p><strong>Thank you for choosing FirmAnt Event Management!</strong></p>
                <p>This receipt was generated on <?php echo date('F j, Y \a\t g:i A'); ?></p>
                <p style="font-size: 10px; color: #999;">Receipt ID: <?php echo $order['order_number']; ?>-<?php echo date('Ymd'); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
