<?php
// PDF Receipt Generator for FirmAnt Event Management System
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and order number is provided
if (!isset($_SESSION['uid']) || !isset($_GET['order'])) {
    header('Location: index.php');
    exit();
}

include_once("Database/connect.php");

$order_number = $_GET['order'];
$user_id = $_SESSION['uid'];

// Get order details
$order_query = "SELECT o.*, u.name as user_name, u.email as user_email, u.phone as user_phone 
                FROM orders o 
                JOIN user u ON o.user_id = u.id 
                WHERE o.order_number = ? AND o.user_id = ?";
$order_stmt = mysqli_prepare($con, $order_query);
mysqli_stmt_bind_param($order_stmt, "si", $order_number, $user_id);
mysqli_stmt_execute($order_stmt);
$order_result = mysqli_stmt_get_result($order_stmt);

if (mysqli_num_rows($order_result) == 0) {
    header('Location: index.php');
    exit();
}

$order = mysqli_fetch_assoc($order_result);

// Get order items
$items_query = "SELECT * FROM order_items WHERE order_id = ?";
$items_stmt = mysqli_prepare($con, $items_query);
mysqli_stmt_bind_param($items_stmt, "i", $order['id']);
mysqli_stmt_execute($items_stmt);
$items_result = mysqli_stmt_get_result($items_stmt);

$order_items = array();
while ($item = mysqli_fetch_assoc($items_result)) {
    $order_items[] = $item;
}

// Simple PDF generation without external libraries
// We'll create an HTML-to-PDF solution using browser printing
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - Order #<?php echo $order['order_number']; ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .receipt-container { box-shadow: none; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .receipt-header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        
        .company-tagline {
            color: #666;
            font-style: italic;
        }
        
        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
            text-align: center;
        }
        
        .order-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section h3 {
            color: #667eea;
            font-size: 16px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .info-section p {
            margin: 5px 0;
            color: #333;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        
        .items-table th {
            background: #667eea;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .total-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #667eea;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 16px;
        }
        
        .total-row.final {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .print-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 10px;
        }
        
        .print-btn:hover {
            background: #5a6fd8;
        }
        
        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 10px;
        }
        
        .download-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Receipt Header -->
        <div class="receipt-header">
            <img src="images/real-logo.png" alt="FirmAnt Logo" class="company-logo">
            <div class="company-name">FirmAnt Event Management</div>
            <div class="company-tagline">Creating Memorable Moments</div>
        </div>
        
        <div class="receipt-title">PAYMENT RECEIPT</div>
        
        <!-- Order Information -->
        <div class="order-info">
            <div class="info-section">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> <?php echo $order['order_number']; ?></p>
                <p><strong>Order Date:</strong> <?php echo date('F j, Y', strtotime($order['created_at'])); ?></p>
                <p><strong>Order Time:</strong> <?php echo date('g:i A', strtotime($order['created_at'])); ?></p>
                <p><strong>Status:</strong> 
                    <span class="status-badge status-<?php echo $order['status']; ?>">
                        <?php echo ucfirst($order['status']); ?>
                    </span>
                </p>
            </div>
            
            <div class="info-section">
                <h3>Customer Information</h3>
                <p><strong>Name:</strong> <?php echo htmlspecialchars($order['user_name']); ?></p>
                <p><strong>Email:</strong> <?php echo htmlspecialchars($order['user_email']); ?></p>
                <?php if (!empty($order['user_phone'])): ?>
                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['user_phone']); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (!empty($order['shipping_address'])): ?>
        <div class="info-section">
            <h3>Shipping Address</h3>
            <p><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
        </div>
        <?php endif; ?>
        
        <!-- Order Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Type</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($order_items as $item): ?>
                <tr>
                    <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                    <td><?php echo ucfirst($item['item_type']); ?></td>
                    <td><?php echo $item['quantity']; ?></td>
                    <td>$<?php echo number_format($item['unit_price'], 2); ?></td>
                    <td>$<?php echo number_format($item['total_price'], 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Total Section -->
        <div class="total-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>$<?php echo number_format($order['total_amount'], 2); ?></span>
            </div>
            <div class="total-row">
                <span>Tax (10%):</span>
                <span>$<?php echo number_format($order['tax_amount'], 2); ?></span>
            </div>
            <div class="total-row final">
                <span>Total Amount Paid:</span>
                <span>$<?php echo number_format($order['final_amount'], 2); ?></span>
            </div>
        </div>
        
        <?php if (!empty($order['notes'])): ?>
        <div class="info-section">
            <h3>Additional Notes</h3>
            <p><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
        </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>Thank you for choosing FirmAnt Event Management!</strong></p>
            <p>For any questions about your order, please contact <NAME_EMAIL></p>
            <p>This receipt was generated on <?php echo date('F j, Y \a\t g:i A'); ?></p>
        </div>
        
        <!-- Action Buttons -->
        <div class="no-print" style="text-align: center; margin-top: 30px;">
            <button onclick="window.print()" class="print-btn">
                <i class="fas fa-print"></i> Print Receipt
            </button>
            <button onclick="downloadPDF()" class="download-btn">
                <i class="fas fa-download"></i> Download PDF
            </button>
            <a href="order_success.php?order=<?php echo $order['order_number']; ?>" class="print-btn" style="text-decoration: none; display: inline-block;">
                <i class="fas fa-arrow-left"></i> Back to Order
            </a>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script>
        function downloadPDF() {
            // Use browser's print to PDF functionality
            window.print();
        }
        
        // Auto-focus for better user experience
        window.onload = function() {
            document.title = "Receipt - Order #<?php echo $order['order_number']; ?>";
        }
    </script>
</body>
</html>
