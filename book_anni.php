<?php
// Start session at the very beginning
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Anniversary Event | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .booking-hero {
            background: var(--warning);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .booking-section {
            padding: 80px 0;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .booking-form {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #fa709a;
            box-shadow: 0 0 0 0.2rem rgba(250, 112, 154, 0.25);
            background: white;
        }

        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-select:focus {
            border-color: #fa709a;
            box-shadow: 0 0 0 0.2rem rgba(250, 112, 154, 0.25);
            background: white;
        }

        .btn-book {
            background: var(--warning);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-book:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(250, 112, 154, 0.4);
            color: white;
        }

        .package-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .package-price {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--warning);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .package-features {
            list-style: none;
            padding: 0;
        }

        .package-features li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .package-features li:last-child {
            border-bottom: none;
        }

        .package-features i {
            color: #28a745;
            margin-right: 10px;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .booking-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Items - Centered with left margin -->
                <ul class="navbar-nav mx-auto align-items-center" style="margin-left: 3rem;">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                </ul>

                <!-- User Actions Group - Right aligned -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item me-3">
                        <?php
                        include_once("cart_functions.php");
                        $cart_count = $cartManager->getCartCount();
                        ?>
                        <a class="nav-link position-relative" href="cart.php" title="View Cart">
                            <i class="fas fa-shopping-cart fa-lg"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;"> <?php echo $cart_count; ?> </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['uid'])): ?>
                        <li class="nav-item me-3">
                            <a class="nav-link d-flex align-items-center" href="my_orders.php">
                                <i class="fas fa-receipt fa-lg me-1"></i> My Orders
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <?php if (isset($_SESSION['uid'])): ?>
                            <a class="nav-link d-flex align-items-center text-danger" href="logout.php?redirect=book_anni">
                                <i class="fas fa-sign-out-alt fa-lg me-1"></i> Sign Out
                            </a>
                        <?php else: ?>
                            <a class="nav-link d-flex align-items-center" href="login.php?redirect=book_anni">
                                <i class="fas fa-sign-in-alt fa-lg me-1"></i> Login
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="booking-hero">
        <div class="container">
            <h1 class="hero-title">Book Anniversary Celebration</h1>
            <p class="hero-subtitle">Celebrate your special milestone with our premium anniversary event planning</p>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking-section">
        <div class="container">
            <div class="row">
                <!-- Booking Form -->
                <div class="col-lg-8">
                    <div class="booking-card" data-aos="fade-up">
                        <div class="booking-form">
                            <h3 class="mb-4">Anniversary Event Details</h3>
                            
                            <?php
                            include_once("Database/connect.php");
                            
                            if(isset($_POST['submit'])) {
                                $name = $_POST['name'];
                                $email = $_POST['email'];
                                $phone = $_POST['phone'];
                                $event_date = $_POST['event_date'];
                                $guests = $_POST['guests'];
                                $venue = $_POST['venue'];
                                $package_id = isset($_GET['id']) ? $_GET['id'] : '';
                                $message = $_POST['message'];
                                $anniversary_year = $_POST['anniversary_year'];
                                
                                $query = "INSERT INTO anniversary_bookings (name, email, phone, event_date, guests, venue, package_id, message, anniversary_year, booking_date) 
                                         VALUES ('$name', '$email', '$phone', '$event_date', '$guests', '$venue', '$package_id', '$message', '$anniversary_year', NOW())";
                                
                                if(mysqli_query($con, $query)) {
                                    echo "<div class='alert alert-success'>Booking request submitted successfully! We'll contact you soon.</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>Error submitting booking. Please try again.</div>";
                                }
                            }
                            ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Event Date</label>
                                            <input type="date" class="form-control" name="event_date" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Anniversary Year</label>
                                            <select class="form-select" name="anniversary_year" required>
                                                <option value="">Select anniversary</option>
                                                <option value="1">1st Anniversary</option>
                                                <option value="5">5th Anniversary</option>
                                                <option value="10">10th Anniversary</option>
                                                <option value="15">15th Anniversary</option>
                                                <option value="20">20th Anniversary</option>
                                                <option value="25">25th Anniversary (Silver)</option>
                                                <option value="30">30th Anniversary</option>
                                                <option value="40">40th Anniversary</option>
                                                <option value="50">50th Anniversary (Golden)</option>
                                                <option value="60">60th Anniversary (Diamond)</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Number of Guests</label>
                                            <select class="form-select" name="guests" required>
                                                <option value="">Select guest count</option>
                                                <option value="10-25">10-25 guests</option>
                                                <option value="25-50">25-50 guests</option>
                                                <option value="50-100">50-100 guests</option>
                                                <option value="100+">100+ guests</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Preferred Venue</label>
                                    <select class="form-select" name="venue" required>
                                        <option value="">Select venue type</option>
                                        <option value="home">Home/Private</option>
                                        <option value="restaurant">Restaurant</option>
                                        <option value="hotel">Hotel</option>
                                        <option value="outdoor">Outdoor/Garden</option>
                                        <option value="banquet">Banquet Hall</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Special Requirements</label>
                                    <textarea class="form-control" name="message" rows="4" placeholder="Tell us about your vision, special requirements, or any specific requests for your anniversary celebration..."></textarea>
                                </div>
                                
                                <button type="submit" name="submit" class="btn-book">
                                    <i class="fas fa-heart me-2"></i>Submit Booking Request
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Package Info -->
                <div class="col-lg-4">
                    <div class="package-card" data-aos="fade-up" data-aos-delay="200">
                        <?php
                        if(isset($_GET['id'])) {
                            $package_id = $_GET['id'];
                            $query = "SELECT * FROM anniversary WHERE id = $package_id";
                            $result = mysqli_query($con, $query);
                            if($package = mysqli_fetch_array($result)) {
                        ?>
                        <div class="text-center mb-4">
                            <img src="images/<?php echo $package['img']; ?>" alt="Package" class="img-fluid rounded-3 mb-3">
                            <h4><?php echo $package['nm']; ?></h4>
                            <div class="package-price">$<?php echo $package['price']; ?></div>
                        </div>
                        <?php
                            }
                        }
                        ?>
                        
                        <h5 class="mb-3">Anniversary Package Includes:</h5>
                        <ul class="package-features">
                            <li><i class="fas fa-check"></i> Elegant decorations</li>
                            <li><i class="fas fa-check"></i> Romantic lighting setup</li>
                            <li><i class="fas fa-check"></i> Professional photography</li>
                            <li><i class="fas fa-check"></i> Custom anniversary cake</li>
                            <li><i class="fas fa-check"></i> Floral arrangements</li>
                            <li><i class="fas fa-check"></i> Music & entertainment</li>
                            <li><i class="fas fa-check"></i> Event coordination</li>
                            <li><i class="fas fa-check"></i> Memory slideshow</li>
                        </ul>
                        
                        <div class="mt-4 p-3 bg-light rounded-3">
                            <h6><i class="fas fa-info-circle me-2"></i>Booking Information</h6>
                            <small class="text-muted">
                                • 50% deposit required to confirm booking<br>
                                • Free consultation included<br>
                                • Cancellation policy applies<br>
                                • Custom packages available<br>
                                • Milestone anniversary specials
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <h4 class="mb-3"><i class="fas fa-calendar-star me-2"></i>FirmAnt Events</h4>
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

