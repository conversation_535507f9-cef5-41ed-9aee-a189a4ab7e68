<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include("Database/connect.php");

// If already logged in, redirect to index.php
if(isset($_SESSION['uid'])) {
    header('Location: index.php');
    exit();
}

if(isset($_POST['submit'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];

    $qry = mysqli_query($con, "SELECT * FROM user WHERE email='$email' AND password='$password'");
    if($row = mysqli_fetch_array($qry)) {
        $_SESSION['uname'] = $row['name'];
        $_SESSION['uid'] = $row['id'];

        // Transfer session cart to user cart
        include_once("cart_functions.php");
        $cartManager->transferSessionCartToUser($row['id']);

        // Redirect to intended page or index
        $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
        header('Location: ' . $redirect . '?login=success');
        exit();
    } else {
        $error = "Invalid email or password!";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        body {
            background: var(--accent);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 20px 0;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('images/cs_birthday1.jpg') center/cover no-repeat;
            opacity: 0.1;
            z-index: 1;
        }
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 2;
        }
        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .auth-container {
            position: relative;
            z-index: 3;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 500px;
            text-align: center;
            min-height: 80vh;
            overflow: visible;
        }
        .brand-logo {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .auth-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
        }
        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
            background: white;
        }
        .btn-auth {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            width: 100%;
            margin: 20px 0;
        }
        .btn-auth:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
            color: white;
        }
        .auth-links {
            margin-top: 20px;
        }
        .auth-links a {
            color: #4facfe;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .auth-links a:hover {
            color: #00f2fe;
            text-decoration: underline;
        }
        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 20px;
        }
        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        @media (max-width: 992px) {
            body {
                padding: 0;
                min-height: 100vh;
            }
            .auth-container {
                padding: 30px 10px;
                max-width: 100vw;
                margin: 0 auto;
                border-radius: 14px;
                min-height: unset;
            }
            .brand-logo img {
                height: 32px !important;
            }
            .auth-title {
                font-size: 1.3rem;
            }
            .form-control {
                padding: 12px 12px;
                font-size: 0.95rem;
            }
            .btn-auth {
                padding: 12px 20px;
                font-size: 1rem;
            }
        }
        @media (max-width: 576px) {
            body {
                padding: 0;
                min-height: 100vh;
            }
            .auth-container {
                padding: 10px 2px;
                max-width: 100vw;
                margin: 0;
                border-radius: 8px;
                min-height: unset;
            }
            .brand-logo img {
                height: 20px !important;
            }
            .auth-title {
                font-size: 1rem;
                margin-bottom: 12px;
            }
            .form-control {
                padding: 6px 6px;
                font-size: 0.9rem;
            }
            .btn-auth {
                padding: 8px 8px;
                font-size: 0.95rem;
            }
            .auth-links {
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>
    <div class="auth-container">
        <div class="brand-logo">
            <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
        </div>
        <h2 class="auth-title">Welcome Back</h2>
        <p class="text-muted mb-4">Sign in to your account to continue</p>
        <?php if(isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            </div>
        <?php endif; ?>
        <form method="post">
            <div class="form-floating mb-3">
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
            </div>
            <div class="form-floating mb-3">
                <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
            </div>
            <button type="submit" name="submit" class="btn btn-auth">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </button>
        </form>
        <div class="auth-links">
            <p class="mb-2">Don't have an account? <a href="registration.php">Create Account</a></p>
            <p><a href="index.php"><i class="fas fa-arrow-left me-2"></i>Back to Home</a></p>
        </div>
    </div>
</body>
</html>

