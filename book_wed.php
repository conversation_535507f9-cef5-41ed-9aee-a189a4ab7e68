<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Wedding Event | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --wedding: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .booking-hero {
            background: var(--wedding);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: #8B4513;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            color: #8B4513;
        }

        .booking-section {
            padding: 80px 0;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .booking-form {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus, .form-select:focus {
            border-color: #fcb69f;
            box-shadow: 0 0 0 0.2rem rgba(252, 182, 159, 0.25);
            background: white;
        }

        .btn-book {
            background: var(--wedding);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: #8B4513;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-book:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(252, 182, 159, 0.4);
            color: #8B4513;
        }

        .package-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            margin-bottom: 20px;
        }

        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .package-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .package-price {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--wedding);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .package-features {
            list-style: none;
            padding: 0;
        }

        .package-features li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .package-features li:last-child {
            border-bottom: none;
        }

        .package-features i {
            color: #28a745;
            margin-right: 10px;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .booking-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Items - Centered with left margin -->
                <ul class="navbar-nav mx-auto align-items-center" style="margin-left: 3rem;">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                </ul>

                <!-- User Actions Group - Right aligned -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item me-3">
                        <?php
                        if (session_status() === PHP_SESSION_NONE) {
                            session_start();
                        }
                        include_once("cart_functions.php");
                        $cart_count = $cartManager->getCartCount();
                        ?>
                        <a class="nav-link position-relative" href="cart.php" title="View Cart">
                            <i class="fas fa-shopping-cart fa-lg"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;"> <?php echo $cart_count; ?> </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['uid'])): ?>
                        <li class="nav-item me-3">
                            <a class="nav-link d-flex align-items-center" href="my_orders.php">
                                <i class="fas fa-receipt fa-lg me-1"></i> My Orders
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <?php if (isset($_SESSION['uid'])): ?>
                            <a class="nav-link d-flex align-items-center text-danger" href="logout.php?redirect=book_wed">
                                <i class="fas fa-sign-out-alt fa-lg me-1"></i> Sign Out
                            </a>
                        <?php else: ?>
                            <a class="nav-link d-flex align-items-center" href="login.php?redirect=book_wed">
                                <i class="fas fa-sign-in-alt fa-lg me-1"></i> Login
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="booking-hero">
        <div class="container">
            <h1 class="hero-title">Book Wedding Event</h1>
            <p class="hero-subtitle">Create your perfect wedding day with our premium planning services</p>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking-section">
        <div class="container">
            <div class="row">
                <!-- Booking Form -->
                <div class="col-lg-8">
                    <div class="booking-card" data-aos="fade-up">
                        <div class="booking-form">
                            <h3 class="mb-4">Wedding Event Details</h3>
                            
                            <?php
                            include_once("Database/connect.php");
                            
                            if(isset($_POST['submit'])) {
                                $bride_name = $_POST['bride_name'];
                                $groom_name = $_POST['groom_name'];
                                $email = $_POST['email'];
                                $phone = $_POST['phone'];
                                $wedding_date = $_POST['wedding_date'];
                                $guests = $_POST['guests'];
                                $venue = $_POST['venue'];
                                $package_id = isset($_GET['id']) ? $_GET['id'] : '';
                                $message = $_POST['message'];
                                $ceremony_type = $_POST['ceremony_type'];
                                $budget_range = $_POST['budget_range'];
                                
                                $query = "INSERT INTO wedding_bookings (bride_name, groom_name, email, phone, wedding_date, guests, venue, package_id, message, ceremony_type, budget_range, booking_date) 
                                         VALUES ('$bride_name', '$groom_name', '$email', '$phone', '$wedding_date', '$guests', '$venue', '$package_id', '$message', '$ceremony_type', '$budget_range', NOW())";
                                
                                if(mysqli_query($con, $query)) {
                                    echo "<div class='alert alert-success'>Wedding booking request submitted successfully! We'll contact you soon to discuss your special day.</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>Error submitting booking. Please try again.</div>";
                                }
                            }
                            ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Bride's Name</label>
                                            <input type="text" class="form-control" name="bride_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Groom's Name</label>
                                            <input type="text" class="form-control" name="groom_name" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Wedding Date</label>
                                            <input type="date" class="form-control" name="wedding_date" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Number of Guests</label>
                                            <select class="form-select" name="guests" required>
                                                <option value="">Select guest count</option>
                                                <option value="50-100">50-100 guests</option>
                                                <option value="100-200">100-200 guests</option>
                                                <option value="200-300">200-300 guests</option>
                                                <option value="300+">300+ guests</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Ceremony Type</label>
                                            <select class="form-select" name="ceremony_type" required>
                                                <option value="">Select ceremony type</option>
                                                <option value="Church Ceremony">Church Ceremony</option>
                                                <option value="Outdoor Ceremony">Outdoor Ceremony</option>
                                                <option value="Indoor Ceremony">Indoor Ceremony</option>
                                                <option value="Civil Ceremony">Civil Ceremony</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Budget Range</label>
                                            <select class="form-select" name="budget_range" required>
                                                <option value="">Select budget range</option>
                                                <option value="Under $5000">$5000 and below</option>
                                                <option value="$5000-$10000">$5000 - $10000</option>
                                                <option value="$10000-$20000">$10000 - $20000</option>
                                                <option value="$20000+">$20000 and above</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Venue</label>
                                    <input type="text" class="form-control" name="venue" placeholder="Enter your preferred venue" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Additional Message</label>
                                    <textarea class="form-control" name="message" rows="4" placeholder="Enter any additional details or special requests..."></textarea>
                                </div>
                                
                                <button type="submit" name="submit" class="btn-book">
                                    <i class="fas fa-heart me-2"></i>Book Your Wedding
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Packages -->
                <div class="col-lg-4">
                    <div class="row g-4">
                        <div class="col-12">
                            <div class="card shadow-lg border-0 package-modern" data-aos="fade-up" style="border-radius:24px;">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <span class="badge bg-gradient text-white" style="background:linear-gradient(135deg,#ffecd2,#fcb69f);font-size:1rem;padding:10px 24px;border-radius:20px;">Basic Package</span>
                                    </div>
                                    <div class="package-price mb-2" style="font-size:2.2rem;font-weight:800;color:#8B4513;">$5,000</div>
                                    <ul class="list-unstyled package-features mb-3 text-start mx-auto" style="max-width:260px;">
                                        <li><i class="fas fa-check text-success me-2"></i>Basic planning services</li>
                                        <li><i class="fas fa-check text-success me-2"></i>100-200 guests</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Basic catering options</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Basic decor setup</li>
                                        <li><i class="fas fa-check text-success me-2"></i>1 hour ceremony</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card shadow-lg border-0 package-modern" data-aos="fade-up" data-aos-delay="100" style="border-radius:24px;">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <span class="badge bg-gradient text-white" style="background:linear-gradient(135deg,#43e97b,#38f9d7);font-size:1rem;padding:10px 24px;border-radius:20px;">Standard Package</span>
                                    </div>
                                    <div class="package-price mb-2" style="font-size:2.2rem;font-weight:800;color:#8B4513;">$10,000</div>
                                    <ul class="list-unstyled package-features mb-3 text-start mx-auto" style="max-width:260px;">
                                        <li><i class="fas fa-check text-success me-2"></i>Full planning services</li>
                                        <li><i class="fas fa-check text-success me-2"></i>200-300 guests</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Premium catering</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Professional decor</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Photography included</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Music & entertainment</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card shadow-lg border-0 package-modern" data-aos="fade-up" data-aos-delay="200" style="border-radius:24px;">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <span class="badge bg-gradient text-white" style="background:linear-gradient(135deg,#667eea,#764ba2);font-size:1rem;padding:10px 24px;border-radius:20px;">Premium Package</span>
                                    </div>
                                    <div class="package-price mb-2" style="font-size:2.2rem;font-weight:800;color:#8B4513;">$20,000</div>
                                    <ul class="list-unstyled package-features mb-3 text-start mx-auto" style="max-width:260px;">
                                        <li><i class="fas fa-check text-success me-2"></i>Luxury planning services</li>
                                        <li><i class="fas fa-check text-success me-2"></i>300+ guests</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Gourmet catering</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Designer decor</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Professional photography & videography</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Live band & DJ</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Bridal suite included</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h4>About FirmAnt</h4>
                    <p>FirmAnt Event Management is dedicated to helping you create unforgettable events! From weddings to corporate gatherings, we've got you covered.</p>
                </div>
                <div class="col-md-6">
                    <h4>Contact Information</h4>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 123 Event Street, Cityville, Country</p>
                    <p><i class="fas fa-phone-alt me-2"></i> +************</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2023 FirmAnt Event Management. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="wedding_date"]');
            const today = new Date().toISOString().split('T')[0];
            dateInput.setAttribute('min', today);
        });
    </script>
</body>
</html>

