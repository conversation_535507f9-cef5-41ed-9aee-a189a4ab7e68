<?php
// Add to <PERSON><PERSON>
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include_once("cart_functions.php");

// Check if this is an AJAX request
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $item_id = isset($_POST['item_id']) ? intval($_POST['item_id']) : 0;
    $item_type = isset($_POST['item_type']) ? $_POST['item_type'] : '';
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    
    // Validate inputs
    if ($item_id <= 0 || empty($item_type) || $quantity <= 0) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Invalid item data']);
            exit;
        } else {
            header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=invalid_data');
            exit;
        }
    }
    
    // Validate item type
    $valid_types = ['wedding', 'birthday', 'anniversary', 'otherevent'];
    if (!in_array($item_type, $valid_types)) {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Invalid item type']);
            exit;
        } else {
            header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=invalid_type');
            exit;
        }
    }
    
    // Add to cart
    $success = $cartManager->addToCart($item_id, $item_type, $quantity);
    
    if ($success) {
        $cart_count = $cartManager->getCartCount();
        
        if ($is_ajax) {
            echo json_encode([
                'success' => true, 
                'message' => 'Item added to cart successfully!',
                'cart_count' => $cart_count
            ]);
            exit;
        } else {
            header('Location: ' . $_SERVER['HTTP_REFERER'] . '?success=added_to_cart');
            exit;
        }
    } else {
        if ($is_ajax) {
            echo json_encode(['success' => false, 'message' => 'Failed to add item to cart']);
            exit;
        } else {
            header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=add_failed');
            exit;
        }
    }
} else {
    // Invalid request method
    if ($is_ajax) {
        echo json_encode(['success' => false, 'message' => 'Invalid request method']);
        exit;
    } else {
        header('Location: index.php');
        exit;
    }
}
?>
