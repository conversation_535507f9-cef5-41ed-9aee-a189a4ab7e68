<?php
// Cart functionality for FirmAnt Event Management System
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include_once("Database/connect.php");

class CartManager {
    private $con;
    
    public function __construct($connection) {
        $this->con = $connection;
    }
    
    // Add item to cart
    public function addToCart($item_id, $item_type, $quantity = 1) {
        if (isset($_SESSION['uid'])) {
            // User is logged in - save to database
            return $this->addToUserCart($item_id, $item_type, $quantity);
        } else {
            // Guest user - save to session
            return $this->addToSessionCart($item_id, $item_type, $quantity);
        }
    }
    
    // Add to logged-in user's cart
    private function addToUserCart($item_id, $item_type, $quantity) {
        $user_id = $_SESSION['uid'];
        
        // Get item details
        $item_details = $this->getItemDetails($item_id, $item_type);
        if (!$item_details) {
            return false;
        }
        
        // Check if item already exists in cart
        $check_query = "SELECT * FROM cart WHERE user_id = ? AND item_id = ? AND item_type = ?";
        $check_stmt = mysqli_prepare($this->con, $check_query);
        mysqli_stmt_bind_param($check_stmt, "iis", $user_id, $item_id, $item_type);
        mysqli_stmt_execute($check_stmt);
        $result = mysqli_stmt_get_result($check_stmt);
        
        if (mysqli_num_rows($result) > 0) {
            // Update quantity
            $update_query = "UPDATE cart SET quantity = quantity + ? WHERE user_id = ? AND item_id = ? AND item_type = ?";
            $update_stmt = mysqli_prepare($this->con, $update_query);
            mysqli_stmt_bind_param($update_stmt, "iiis", $quantity, $user_id, $item_id, $item_type);
            return mysqli_stmt_execute($update_stmt);
        } else {
            // Insert new item
            $insert_query = "INSERT INTO cart (user_id, item_id, item_type, quantity, price) VALUES (?, ?, ?, ?, ?)";
            $insert_stmt = mysqli_prepare($this->con, $insert_query);
            mysqli_stmt_bind_param($insert_stmt, "iisid", $user_id, $item_id, $item_type, $quantity, $item_details['price']);
            return mysqli_stmt_execute($insert_stmt);
        }
    }
    
    // Add to session cart for guest users
    private function addToSessionCart($item_id, $item_type, $quantity) {
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = array();
        }
        
        $item_details = $this->getItemDetails($item_id, $item_type);
        if (!$item_details) {
            return false;
        }
        
        $cart_key = $item_type . '_' . $item_id;
        
        if (isset($_SESSION['cart'][$cart_key])) {
            $_SESSION['cart'][$cart_key]['quantity'] += $quantity;
        } else {
            $_SESSION['cart'][$cart_key] = array(
                'id' => $item_id,
                'type' => $item_type,
                'name' => $item_details['name'],
                'image' => $item_details['image'],
                'price' => $item_details['price'],
                'quantity' => $quantity,
                'description' => $item_details['description'] ?? ''
            );
        }
        
        return true;
    }
    
    // Get item details from database
    private function getItemDetails($item_id, $item_type) {
        $table_map = array(
            'wedding' => 'wedding',
            'birthday' => 'birthday',
            'anniversary' => 'anniversary',
            'otherevent' => 'otherevent'
        );
        
        if (!isset($table_map[$item_type])) {
            return false;
        }
        
        $table = $table_map[$item_type];
        $query = "SELECT id, nm as name, img as image, price FROM $table WHERE id = ?";
        $stmt = mysqli_prepare($this->con, $query);
        mysqli_stmt_bind_param($stmt, "i", $item_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if ($row = mysqli_fetch_assoc($result)) {
            $row['description'] = "Premium " . ucfirst($item_type) . " Package";
            return $row;
        }
        
        return false;
    }
    
    // Get cart items for logged-in user
    public function getUserCartItems($user_id) {
        $query = "SELECT c.*, 
                         CASE 
                             WHEN c.item_type = 'wedding' THEN w.nm
                             WHEN c.item_type = 'birthday' THEN b.nm
                             WHEN c.item_type = 'anniversary' THEN a.nm
                             WHEN c.item_type = 'otherevent' THEN o.nm
                         END as item_name,
                         CASE 
                             WHEN c.item_type = 'wedding' THEN w.img
                             WHEN c.item_type = 'birthday' THEN b.img
                             WHEN c.item_type = 'anniversary' THEN a.img
                             WHEN c.item_type = 'otherevent' THEN o.img
                         END as item_image
                  FROM cart c
                  LEFT JOIN wedding w ON c.item_type = 'wedding' AND c.item_id = w.id
                  LEFT JOIN birthday b ON c.item_type = 'birthday' AND c.item_id = b.id
                  LEFT JOIN anniversary a ON c.item_type = 'anniversary' AND c.item_id = a.id
                  LEFT JOIN otherevent o ON c.item_type = 'otherevent' AND c.item_id = o.id
                  WHERE c.user_id = ?";
        
        $stmt = mysqli_prepare($this->con, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $items = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $items[] = $row;
        }
        
        return $items;
    }
    
    // Remove item from cart
    public function removeFromCart($item_id, $item_type) {
        if (isset($_SESSION['uid'])) {
            $user_id = $_SESSION['uid'];
            $query = "DELETE FROM cart WHERE user_id = ? AND item_id = ? AND item_type = ?";
            $stmt = mysqli_prepare($this->con, $query);
            mysqli_stmt_bind_param($stmt, "iis", $user_id, $item_id, $item_type);
            return mysqli_stmt_execute($stmt);
        } else {
            $cart_key = $item_type . '_' . $item_id;
            if (isset($_SESSION['cart'][$cart_key])) {
                unset($_SESSION['cart'][$cart_key]);
                return true;
            }
        }
        return false;
    }
    
    // Update cart item quantity
    public function updateCartQuantity($item_id, $item_type, $quantity) {
        if ($quantity <= 0) {
            return $this->removeFromCart($item_id, $item_type);
        }
        
        if (isset($_SESSION['uid'])) {
            $user_id = $_SESSION['uid'];
            $query = "UPDATE cart SET quantity = ? WHERE user_id = ? AND item_id = ? AND item_type = ?";
            $stmt = mysqli_prepare($this->con, $query);
            mysqli_stmt_bind_param($stmt, "iiis", $quantity, $user_id, $item_id, $item_type);
            return mysqli_stmt_execute($stmt);
        } else {
            $cart_key = $item_type . '_' . $item_id;
            if (isset($_SESSION['cart'][$cart_key])) {
                $_SESSION['cart'][$cart_key]['quantity'] = $quantity;
                return true;
            }
        }
        return false;
    }
    
    // Get cart count
    public function getCartCount() {
        if (isset($_SESSION['uid'])) {
            $user_id = $_SESSION['uid'];
            $query = "SELECT SUM(quantity) as total FROM cart WHERE user_id = ?";
            $stmt = mysqli_prepare($this->con, $query);
            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            return $row['total'] ?? 0;
        } else {
            return isset($_SESSION['cart']) ? array_sum(array_column($_SESSION['cart'], 'quantity')) : 0;
        }
    }
    
    // Transfer session cart to user cart when user logs in
    public function transferSessionCartToUser($user_id) {
        if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
            foreach ($_SESSION['cart'] as $item) {
                $this->addToUserCart($item['id'], $item['type'], $item['quantity']);
            }
            // Clear session cart
            unset($_SESSION['cart']);
        }
    }
    
    // Clear cart
    public function clearCart() {
        if (isset($_SESSION['uid'])) {
            $user_id = $_SESSION['uid'];
            $query = "DELETE FROM cart WHERE user_id = ?";
            $stmt = mysqli_prepare($this->con, $query);
            mysqli_stmt_bind_param($stmt, "i", $user_id);
            return mysqli_stmt_execute($stmt);
        } else {
            unset($_SESSION['cart']);
            return true;
        }
    }
}

// Initialize cart manager
$cartManager = new CartManager($con);
?>
