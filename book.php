<?php
// Start session at the very beginning
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include_once("Database/connect.php");
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';
require_once("config/smtp_config.php"); // Use same SMTP config as contact.php

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

if(isset($_POST['submit'])) {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $event_type = $_POST['event_type'];
    $event_date = $_POST['event_date'];
    $guests = $_POST['guests'];
    $venue = $_POST['venue'];
    $budget = $_POST['budget'];
    $message = $_POST['message'];
    $query = "INSERT INTO general_bookings (name, email, phone, event_type, event_date, guests, venue, budget, message, booking_date) VALUES ('$name', '$email', '$phone', '$event_type', '$event_date', '$guests', '$venue', '$budget', '$message', NOW())";
    if(mysqli_query($con, $query)) {
        $mail = new PHPMailer(true);
        try {
            // SMTP settings from smtp_config.php
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'rrwp eavq ooww ucol';
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = SMTP_PORT;

            $mail->setFrom('<EMAIL>', 'FirmAnt Events');
            $mail->addAddress('<EMAIL>'); // Admin email
            $mail->addReplyTo($email, $name);

            $mail->isHTML(true);
            $mail->Subject = 'New Event Booking - FirmAnt Events';
            $mail->Body = "<h2>New Event Booking</h2>"
                . "<p><strong>Name:</strong> $name</p>"
                . "<p><strong>Email:</strong> $email</p>"
                . "<p><strong>Phone:</strong> $phone</p>"
                . "<p><strong>Event Type:</strong> $event_type</p>"
                . "<p><strong>Event Date:</strong> $event_date</p>"
                . "<p><strong>Guests:</strong> $guests</p>"
                . "<p><strong>Venue:</strong> $venue</p>"
                . "<p><strong>Budget:</strong> $budget</p>"
                . "<p><strong>Message:</strong> $message</p>"
                . "<p><strong>Booking Date:</strong> " . date('Y-m-d H:i:s') . "</p>";
            $mail->AltBody = "New Event Booking\n"
                . "Name: $name\n"
                . "Email: $email\n"
                . "Phone: $phone\n"
                . "Event Type: $event_type\n"
                . "Event Date: $event_date\n"
                . "Guests: $guests\n"
                . "Venue: $venue\n"
                . "Budget: $budget\n"
                . "Message: $message\n"
                . "Booking Date: " . date('Y-m-d H:i:s');

            $mail->send();
            header('Location: booking_success.php');
            exit();
        } catch (Exception $e) {
            $error_message = "<div class='alert alert-danger'>Booking saved, but email could not be sent. Mailer Error: {$mail->ErrorInfo}</div>";
        }
    } else {
        $error_message = "<div class='alert alert-danger'>Error submitting booking. Please try again.</div>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Event | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .booking-hero {
            background: var(--primary);
            padding: 120px 0 80px;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .booking-section {
            padding: 80px 0;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .booking-form {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .btn-book {
            background: var(--primary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-book:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .event-type-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .event-type-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .event-type-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
        }

        .event-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .event-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .event-description {
            color: #666;
            font-size: 0.9rem;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .booking-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Items - Centered with left margin -->
                <ul class="navbar-nav mx-auto align-items-center" style="margin-left: 3rem;">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                </ul>

                <!-- User Actions Group - Right aligned -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item me-3">
                        <?php
                        include_once("cart_functions.php");
                        $cart_count = $cartManager->getCartCount();
                        ?>
                        <a class="nav-link position-relative" href="cart.php" title="View Cart">
                            <i class="fas fa-shopping-cart fa-lg"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;"> <?php echo $cart_count; ?> </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['uid'])): ?>
                        <li class="nav-item me-3">
                            <a class="nav-link d-flex align-items-center" href="my_orders.php">
                                <i class="fas fa-receipt fa-lg me-1"></i> My Orders
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <?php if (isset($_SESSION['uid'])): ?>
                            <a class="nav-link d-flex align-items-center text-danger" href="logout.php?redirect=book">
                                <i class="fas fa-sign-out-alt fa-lg me-1"></i> Sign Out
                            </a>
                        <?php else: ?>
                            <a class="nav-link d-flex align-items-center" href="login.php?redirect=book">
                                <i class="fas fa-sign-in-alt fa-lg me-1"></i> Login
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="booking-hero">
        <div class="container">
            <h1 class="hero-title">Book Your Event</h1>
            <p class="hero-subtitle">Choose your event type and let us create something extraordinary</p>
        </div>
    </section>

    <!-- Event Selection Section -->
    <section class="booking-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="mb-4">Select Your Event Type</h2>
                    <p class="text-muted">Choose the type of event you'd like to book</p>
                </div>
            </div>

            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="event-type-card" data-aos="fade-up" onclick="selectEventType('wedding')">
                        <div class="event-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h4 class="event-title">Wedding</h4>
                        <p class="event-description">Elegant wedding ceremonies and receptions with premium planning services</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="event-type-card" data-aos="fade-up" data-aos-delay="100" onclick="selectEventType('birthday')">
                        <div class="event-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h4 class="event-title">Birthday</h4>
                        <p class="event-description">Memorable birthday celebrations for all ages with themed decorations</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="event-type-card" data-aos="fade-up" data-aos-delay="200" onclick="selectEventType('anniversary')">
                        <div class="event-icon">
                            <i class="fas fa-ring"></i>
                        </div>
                        <h4 class="event-title">Anniversary</h4>
                        <p class="event-description">Romantic anniversary celebrations to commemorate your special milestones</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="event-type-card" data-aos="fade-up" data-aos-delay="300" onclick="selectEventType('other')">
                        <div class="event-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h4 class="event-title">Other Events</h4>
                        <p class="event-description">Corporate events, parties, and other special occasions</p>
                    </div>
                </div>
            </div>

            <!-- Booking Form -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="booking-card" data-aos="fade-up">
                        <div class="booking-form">
                            <h3 class="mb-4">General Event Booking</h3>
                            
                            <?php
                            if(isset($error_message)) {
                                echo $error_message;
                            }
                            ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Event Type</label>
                                            <select class="form-select" name="event_type" id="eventTypeSelect" required>
                                                <option value="">Select event type</option>
                                                <option value="wedding">Wedding</option>
                                                <option value="birthday">Birthday Party</option>
                                                <option value="anniversary">Anniversary</option>
                                                <option value="corporate">Corporate Event</option>
                                                <option value="graduation">Graduation</option>
                                                <option value="baby_shower">Baby Shower</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Event Date</label>
                                            <input type="date" class="form-control" name="event_date" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Number of Guests</label>
                                            <select class="form-select" name="guests" required>
                                                <option value="">Select guest count</option>
                                                <option value="1-25">1-25 guests</option>
                                                <option value="25-50">25-50 guests</option>
                                                <option value="50-100">50-100 guests</option>
                                                <option value="100-200">100-200 guests</option>
                                                <option value="200+">200+ guests</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Preferred Venue</label>
                                            <select class="form-select" name="venue" required>
                                                <option value="">Select venue type</option>
                                                <option value="home">Home/Private</option>
                                                <option value="restaurant">Restaurant</option>
                                                <option value="hotel">Hotel</option>
                                                <option value="outdoor">Outdoor/Garden</option>
                                                <option value="banquet">Banquet Hall</option>
                                                <option value="office">Office/Corporate</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Budget Range</label>
                                            <select class="form-select" name="budget" required>
                                                <option value="">Select budget range</option>
                                                <option value="500-1000">$500 - $1,000</option>
                                                <option value="1000-2500">$1,000 - $2,500</option>
                                                <option value="2500-5000">$2,500 - $5,000</option>
                                                <option value="5000-10000">$5,000 - $10,000</option>
                                                <option value="10000+">$10,000+</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Event Details & Special Requirements</label>
                                    <textarea class="form-control" name="message" rows="4" placeholder="Tell us about your vision, special requirements, themes, or any specific requests for your event..."></textarea>
                                </div>
                                
                                <button type="submit" name="submit" class="btn-book">
                                    <i class="fas fa-calendar-check me-2"></i>Submit Booking Request
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <h4 class="mb-3"><img src="images/real-logo.png" alt="FirmAnt Logo" style="height:32px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt Events</span></h4>
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        function selectEventType(type) {
            // Remove selected class from all cards
            document.querySelectorAll('.event-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Update the select dropdown
            document.getElementById('eventTypeSelect').value = type;
        }

        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

