<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact | FirmAnt Event Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark: #2c3e50;
            --light: #ecf0f1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
        }

        /* Animated Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 4px 30px rgba(0,0,0,0.15);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        /* Contact Hero Section */
        .contact-hero {
            background: var(--primary);
            min-height: 70vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            animation: fadeInUp 1s ease;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.3s both;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Contact Section */
        .contact-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .contact-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
        }

        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.2);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: white;
            font-size: 2rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-contact {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-contact:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
            color: white;
        }

        /* Map Section */
        .map-section {
            padding: 80px 0;
            background: white;
        }

        .map-container {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        }

        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }

        .footer-brand {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            margin-right: 10px;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
        }

        .social-links a:hover {
            background: var(--primary);
            transform: translateY(-3px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .contact-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link active" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-envelope me-3"></i>Get In <span style="background: var(--secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Touch</span>
                </h1>
                <p class="hero-subtitle">Let's discuss your next event and make it extraordinary</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="row g-4">
                <!-- Contact Info -->
                <div class="col-lg-4">
                    <div class="contact-card text-center" data-aos="fade-up" data-aos-delay="100">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h4 class="mb-3">Call Us</h4>
                        <p class="text-muted mb-3">Ready to help you plan your perfect event</p>
                        <h5 class="text-primary">
                            <a href="tel:+237651734631" style="color: inherit; text-decoration: none;">+237 651 734 631</a>
                        </h5>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="contact-card text-center" data-aos="fade-up" data-aos-delay="200">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4 class="mb-3">Email Us</h4>
                        <p class="text-muted mb-3">Send us your requirements and get a quote</p>
                        <h5 class="text-primary">
                            <a href="mailto:<EMAIL>" style="color: inherit; text-decoration: none;"><EMAIL></a>
                        </h5>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="contact-card text-center" data-aos="fade-up" data-aos-delay="300">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h4 class="mb-3">Visit Us</h4>
                        <p class="text-muted mb-3">Come and see our event planning facilities</p>
                        <h5 class="text-primary">Your Location Here</h5>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="row mt-5">
                <div class="col-lg-8 mx-auto">
                    <!-- Success/Error Messages -->
                    <?php if(isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert" style="border-radius: 15px; border: none; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <?php if(isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert" style="border-radius: 15px; border: none; box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="contact-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-center mb-4">
                            <h3 class="mb-3">Send Us a Message</h3>
                            <p class="text-muted">Fill out the form below and we'll get back to you within 24 hours</p>
                        </div>
                        
                        <form action="" method="post">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                                <div class="col-md-6">
                                    <label for="event_type" class="form-label">Event Type</label>
                                    <select class="form-control" id="event_type" name="event_type">
                                        <option value="">Select Event Type</option>
                                        <option value="wedding">Wedding</option>
                                        <option value="birthday">Birthday Party</option>
                                        <option value="anniversary">Anniversary</option>
                                        <option value="corporate">Corporate Event</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="5" placeholder="Tell us about your event requirements..." required></textarea>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" name="submit" class="btn btn-contact">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Find Us</h2>
                <p class="lead text-muted">Visit our office for a personal consultation</p>
            </div>
            
            <div class="map-container" data-aos="fade-up">
                <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.1422937950147!2d-73.98731968482413!3d40.75889497932681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25855c6480299%3A0x55194ec5a1ae072e!2sTimes+Square!5e0!3m2!1sen!2sus!4v1510579767645" 
                    width="100%" 
                    height="400" 
                    style="border:0;" 
                    allowfullscreen="" 
                    loading="lazy">
                </iframe>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="footer-brand mb-3">
                        <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:32px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt Events</span>
                    </div>
                    <p class="text-muted">Creating unforgettable moments and extraordinary experiences for every special occasion.</p>
                    <div class="social-links mt-4">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Services</h5>
                    <ul class="list-unstyled">
                        <li><a href="gallery.php" class="text-muted">Weddings</a></li>
                        <li><a href="bday_gal.php" class="text-muted">Birthday Parties</a></li>
                        <li><a href="anni_gal.php" class="text-muted">Anniversaries</a></li>
                        <li><a href="other_gal.php" class="text-muted">Corporate Events</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted">Home</a></li>
                        <li><a href="gallery.php" class="text-muted">Gallery</a></li>
                        <li><a href="projects.php" class="text-muted">Projects</a></li>
                        <li><a href="contact.php" class="text-muted">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">Contact Info</h5>
                    <p class="text-muted"><i class="fas fa-phone me-2"></i>+91 90333 36811</p>
                    <p class="text-muted"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt me-2"></i>Your Location Here</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

<?php
// Include database connection
include_once("Database/connect.php");

// Include PHPMailer manually
require_once 'PHPMailer/src/Exception.php';
require_once 'PHPMailer/src/PHPMailer.php';
require_once 'PHPMailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

if(isset($_POST['submit'])) {
    $name = mysqli_real_escape_string($con, $_POST['name']);
    $email = mysqli_real_escape_string($con, $_POST['email']);
    $phone = mysqli_real_escape_string($con, $_POST['phone']);
    $event_type = mysqli_real_escape_string($con, $_POST['event_type']);
    $message = mysqli_real_escape_string($con, $_POST['message']);
    
    // Store in database first
    $query = "INSERT INTO contact_messages (name, email, phone, event_type, message, created_at) 
              VALUES ('$name', '$email', '$phone', '$event_type', '$message', NOW())";
    
    if(mysqli_query($con, $query)) {
        // Create PHPMailer instance
        $mail = new PHPMailer(true);
        
        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host       = 'smtp.gmail.com';
            $mail->SMTPAuth   = true;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'rrwp eavq ooww ucol'; // Replace with your Gmail App Password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port       = 587;
            
            // Recipients
            $mail->setFrom('<EMAIL>', 'FirmAnt Events');
            $mail->addAddress('<EMAIL>', 'FirmAnt Events');
            $mail->addReplyTo($email, $name);
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = 'New Contact Form Submission - FirmAnt Events';
            
            // Create HTML email content
            $mail->Body = "
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                    .container { max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                    .header h2 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }
                    .content { padding: 40px 30px; background: #f8f9fa; }
                    .field { margin-bottom: 25px; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 3px 10px rgba(0,0,0,0.08); }
                    .label { background: #667eea; color: white; padding: 15px 20px; font-weight: bold; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; }
                    .value { padding: 20px; font-size: 16px; line-height: 1.6; color: #2c3e50; }
                    .footer { background: #2c3e50; color: white; padding: 25px; text-align: center; }
                    .footer p { margin: 8px 0; font-size: 14px; }
                    .highlight { color: #667eea; font-weight: 600; }
                    .divider { height: 3px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 30px 0; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>🎉 New Contact Form Submission</h2>
                        <p>FirmAnt Event Management System</p>
                    </div>
                    <div class='content'>
                        <div class='field'>
                            <div class='label'>👤 Full Name</div>
                            <div class='value'><strong>$name</strong></div>
                        </div>
                        <div class='field'>
                            <div class='label'>📧 Email Address</div>
                            <div class='value'><a href='mailto:$email' style='color: #667eea; text-decoration: none;'>$email</a></div>
                        </div>
                        <div class='field'>
                            <div class='label'>📱 Phone Number</div>
                            <div class='value'><a href='tel:$phone' style='color: #667eea; text-decoration: none;'>$phone</a></div>
                        </div>
                        <div class='field'>
                            <div class='label'>🎊 Event Type</div>
                            <div class='value'><span class='highlight'>" . ucfirst($event_type) . "</span></div>
                        </div>
                        <div class='field'>
                            <div class='label'>💬 Message</div>
                            <div class='value'>" . nl2br(htmlspecialchars($message)) . "</div>
                        </div>
                        <div class='divider'></div>
                        <div style='text-align: center; padding: 20px; background: white; border-radius: 12px;'>
                            <h3 style='color: #667eea; margin: 0 0 10px 0;'>Quick Actions</h3>
                            <p style='margin: 0; color: #666;'>
                                <a href='mailto:$email?subject=Re: Your Event Inquiry' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 5px;'>Reply via Email</a>
                                <a href='tel:$phone' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 5px;'>Call Now</a>
                            </p>
                        </div>
                    </div>
                    <div class='footer'>
                        <p><strong>📅 Received on:</strong> " . date('F j, Y \a\t g:i A') . "</p>
                        <p><strong>🌐 Website:</strong> firmantevents.com</p>
                        <p><strong>📞 Contact:</strong> +237 651 734 631</p>
                        <p style='margin-top: 15px; opacity: 0.8; font-size: 12px;'>This email was automatically generated from the FirmAnt Events contact form</p>
                    </div>
                </div>
            </body>
            </html>";
            
            // Plain text version
            $mail->AltBody = "New Contact Form Submission\n\n" .
                           "Name: $name\n" .
                           "Email: $email\n" .
                           "Phone: $phone\n" .
                           "Event Type: " . ucfirst($event_type) . "\n" .
                           "Message: $message\n\n" .
                           "Received on: " . date('F j, Y \a\t g:i A');

            if ($mail->send()) {
                header('Location: contact.php?success=1');
                exit();
            } else {
                $error_message = "❌ Sorry, your message could not be sent. Error: " . $mail->ErrorInfo;
            }
        } catch (Exception $e) {
            $error_message = "❌ Sorry, your message could not be sent. Error: " . $mail->ErrorInfo;
            error_log("Email sending failed: " . $mail->ErrorInfo);
        }
    } else {
        $error_message = "❌ Sorry, there was an error processing your message. Please try again or contact us directly at +237 651 734 631.";
    }
}
?>




