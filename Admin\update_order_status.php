<?php
include_once('../Database/connect.php');
include_once('session.php');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['order_id']) && isset($_POST['status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    
    // Validate status
    $valid_statuses = ['pending', 'confirmed', 'processing', 'completed', 'cancelled'];
    if (!in_array($status, $valid_statuses)) {
        header('Location: view_cart_orders.php?error=invalid_status');
        exit();
    }
    
    // Update order status
    $update_query = "UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?";
    $update_stmt = mysqli_prepare($con, $update_query);
    mysqli_stmt_bind_param($update_stmt, "si", $status, $order_id);
    
    if (mysqli_stmt_execute($update_stmt)) {
        header('Location: view_cart_orders.php?success=status_updated');
    } else {
        header('Location: view_cart_orders.php?error=update_failed');
    }
} else {
    header('Location: view_cart_orders.php');
}
exit();
?>
