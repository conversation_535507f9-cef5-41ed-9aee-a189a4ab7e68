<?php
// Download PHPMailer automatically
$phpmailer_url = "https://github.com/PHPMailer/PHPMailer/archive/refs/heads/master.zip";
$zip_file = "phpmailer.zip";

// Create PHPMailer directory if it doesn't exist
if (!file_exists('PHPMailer')) {
    mkdir('PHPMailer', 0755, true);
}

// Download the zip file
echo "Downloading PHPMailer...\n";
file_put_contents($zip_file, file_get_contents($phpmailer_url));

// Extract the zip file
$zip = new ZipArchive;
if ($zip->open($zip_file) === TRUE) {
    $zip->extractTo('./');
    $zip->close();
    
    // Move files to correct location
    if (file_exists('PHPMailer-master/src')) {
        rename('PHPMailer-master/src', 'PHPMailer/src');
        // Clean up
        rmdir('PHPMailer-master');
    }
    
    unlink($zip_file);
    echo "PHPMailer installed successfully!\n";
} else {
    echo "Failed to extract PHPMailer\n";
}
?>
