<?php
include_once('../Database/connect.php');
include_once('session.php');

// Get all orders with user details
$orders_query = "SELECT o.*, u.name as user_name, u.email as user_email 
                 FROM orders o 
                 JOIN user u ON o.user_id = u.id 
                 ORDER BY o.created_at DESC";
$orders_result = mysqli_query($con, $orders_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Orders | Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .order-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .order-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .order-body {
            padding: 20px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .btn-view-details {
            background: #667eea;
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        .btn-view-details:hover {
            background: #5a6fd8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-shopping-cart me-2"></i>Cart Orders Management</h2>
                <a href="index.php" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (mysqli_num_rows($orders_result) > 0): ?>
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>Recent Orders</h4>
                        <span class="badge bg-primary"><?php echo mysqli_num_rows($orders_result); ?> Total Orders</span>
                    </div>
                </div>
            </div>

            <?php while ($order = mysqli_fetch_assoc($orders_result)): ?>
                <div class="order-card">
                    <div class="order-header">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <strong>Order #<?php echo $order['order_number']; ?></strong>
                            </div>
                            <div class="col-md-3">
                                <i class="fas fa-user me-2"></i><?php echo $order['user_name']; ?>
                            </div>
                            <div class="col-md-3">
                                <i class="fas fa-calendar me-2"></i><?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                            </div>
                            <div class="col-md-3 text-end">
                                <span class="status-badge badge bg-<?php 
                                    echo $order['status'] == 'pending' ? 'warning' : 
                                        ($order['status'] == 'confirmed' ? 'success' : 
                                        ($order['status'] == 'cancelled' ? 'danger' : 'info')); 
                                ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-2"><strong>Customer:</strong> <?php echo $order['user_email']; ?></p>
                                <p class="mb-2"><strong>Total Amount:</strong> $<?php echo number_format($order['final_amount'], 2); ?></p>
                                <p class="mb-0"><strong>Payment Status:</strong> 
                                    <span class="badge bg-<?php echo $order['payment_status'] == 'paid' ? 'success' : 'warning'; ?>">
                                        <?php echo ucfirst($order['payment_status']); ?>
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <?php if (!empty($order['shipping_address'])): ?>
                                    <p class="mb-2"><strong>Shipping Address:</strong></p>
                                    <p class="text-muted small"><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-view-details" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                    <i class="fas fa-eye me-2"></i>View Order Items
                                </button>
                                <div class="btn-group ms-2">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        Update Status
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'pending')">Pending</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'confirmed')">Confirmed</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'processing')">Processing</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'completed')">Completed</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'cancelled')">Cancel</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Order Items (Initially Hidden) -->
                        <div id="order-items-<?php echo $order['id']; ?>" class="mt-3" style="display: none;">
                            <hr>
                            <h6>Order Items:</h6>
                            <?php
                            $items_query = "SELECT * FROM order_items WHERE order_id = " . $order['id'];
                            $items_result = mysqli_query($con, $items_query);
                            ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Type</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($item = mysqli_fetch_assoc($items_result)): ?>
                                            <tr>
                                                <td><?php echo $item['item_name']; ?></td>
                                                <td><span class="badge bg-secondary"><?php echo ucfirst($item['item_type']); ?></span></td>
                                                <td><?php echo $item['quantity']; ?></td>
                                                <td>$<?php echo number_format($item['unit_price'], 2); ?></td>
                                                <td>$<?php echo number_format($item['total_price'], 2); ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>

        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h4>No Orders Found</h4>
                <p class="text-muted">No cart orders have been placed yet.</p>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewOrderDetails(orderId) {
            const detailsDiv = document.getElementById('order-items-' + orderId);
            if (detailsDiv.style.display === 'none') {
                detailsDiv.style.display = 'block';
            } else {
                detailsDiv.style.display = 'none';
            }
        }

        function updateOrderStatus(orderId, status) {
            if (confirm('Are you sure you want to update the order status to ' + status + '?')) {
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'update_order_status.php';
                
                const orderIdInput = document.createElement('input');
                orderIdInput.type = 'hidden';
                orderIdInput.name = 'order_id';
                orderIdInput.value = orderId;
                
                const statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'status';
                statusInput.value = status;
                
                form.appendChild(orderIdInput);
                form.appendChild(statusInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
