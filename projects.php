<?php
include_once("Database/connect.php");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects | FirmAnt Event Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark: #1a1a2e;
            --light: #ffffff;
        }

        body {
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
            padding-top: 80px;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 2px 30px rgba(0,0,0,0.15);
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: var(--dark) !important;
            position: relative;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-link.active {
            color: #667eea !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .projects-hero {
            background: var(--primary);
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            text-align: center;
            color: white;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .projects-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 50px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .filter-btn {
            padding: 12px 30px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            color: #666;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .project-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .project-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }

        .project-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .project-card:hover .project-image img {
            transform: scale(1.1);
        }

        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(102, 126, 234, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .project-card:hover .project-overlay {
            opacity: 1;
        }

        .overlay-content {
            text-align: center;
            color: white;
        }

        .project-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: var(--secondary);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .card-body {
            padding: 25px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark);
        }

        .project-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .project-description {
            color: #777;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .btn-view {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .stats-section {
            background: var(--dark);
            color: white;
            padding: 60px 0;
        }

        .stat-item {
            text-align: center;
            margin-bottom: 30px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            margin-top: 10px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .filter-tabs {
                justify-content: center;
            }
            
            .project-image {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.php#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link active" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Projects Hero Section -->
    <section class="projects-hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-project-diagram me-3"></i>Our <span style="background: var(--secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Projects</span>
                </h1>
                <p class="hero-subtitle">Explore our portfolio of successful events and celebrations</p>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section class="projects-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Our Recent Projects</h2>
                <p class="section-subtitle">Discover the magic we've created for our clients across different event categories</p>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs" data-aos="fade-up" data-aos-delay="100">
                <a href="?category=all" class="filter-btn <?php echo (!isset($_GET['category']) || $_GET['category'] == 'all') ? 'active' : ''; ?>">All Projects</a>
                <a href="?category=wedding" class="filter-btn <?php echo (isset($_GET['category']) && $_GET['category'] == 'wedding') ? 'active' : ''; ?>">Weddings</a>
                <a href="?category=birthday" class="filter-btn <?php echo (isset($_GET['category']) && $_GET['category'] == 'birthday') ? 'active' : ''; ?>">Birthdays</a>
                <a href="?category=anniversary" class="filter-btn <?php echo (isset($_GET['category']) && $_GET['category'] == 'anniversary') ? 'active' : ''; ?>">Anniversaries</a>
                <a href="?category=corporate" class="filter-btn <?php echo (isset($_GET['category']) && $_GET['category'] == 'corporate') ? 'active' : ''; ?>">Corporate</a>
            </div>
            
            <div class="row g-4">
                <?php
                $category = isset($_GET['category']) ? $_GET['category'] : 'all';
                $delay = 100;
                
                // Function to display projects from a table
                function displayProjects($con, $table, $categoryName, $bookingPage, $delay) {
                    $qry = "SELECT * FROM $table ORDER BY id DESC LIMIT 6";
                    $res = mysqli_query($con, $qry);
                    
                    if($res && mysqli_num_rows($res) > 0) {
                        while($row = mysqli_fetch_array($res)) {
                            $currentDelay = $delay;
                            $delay += 100;
                            if($delay > 800) $delay = 100;
                ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $currentDelay; ?>">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="images/<?php echo $row['img']; ?>" alt="<?php echo isset($row['nm']) ? $row['nm'] : $categoryName . ' Event'; ?>">
                            <div class="project-category"><?php echo ucfirst($categoryName); ?></div>
                            <div class="project-overlay">
                                <div class="overlay-content">
                                    <i class="fas fa-eye fa-3x mb-3"></i>
                                    <h5>View Gallery</h5>
                                    <p>Explore this beautiful event</p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo isset($row['nm']) ? $row['nm'] : $categoryName . ' Package'; ?></h5>
                            <p class="project-meta">
                                <i class="fas fa-calendar me-2"></i>
                                <?php echo date('F Y', strtotime('-' . rand(1,12) . ' months')); ?>
                            </p>
                            <p class="project-description">
                                <?php 
                                $descriptions = [
                                    'wedding' => 'A breathtaking celebration with elegant decorations and premium arrangements.',
                                    'birthday' => 'A magical themed celebration with custom decorations and entertainment.',
                                    'anniversary' => 'A heartwarming milestone celebration with family and friends.',
                                    'corporate' => 'Professional event with sophisticated setup and premium services.'
                                ];
                                echo $descriptions[$categoryName] ?? 'A beautiful event celebration with premium arrangements.';
                                ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="images/<?php echo $row['img']; ?>" data-lightbox="projects-gallery" data-title="<?php echo isset($row['nm']) ? $row['nm'] : $categoryName . ' Event'; ?>" class="btn-view">
                                    <i class="fas fa-search-plus me-2"></i>View Images
                                </a>
                                <?php if(isset($row['price'])): ?>
                                    <span class="text-muted">₹<?php echo number_format($row['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            <a href="<?php echo $bookingPage; ?>?id=<?php echo $row['id']; ?>" class="btn-view w-100 mt-3">
                                <i class="fas fa-calendar-plus me-2"></i>Book Similar Event
                            </a>
                        </div>
                    </div>
                </div>
                <?php
                        }
                    }
                    return $delay;
                }

                // Display projects based on category filter
                if($category == 'all' || $category == 'wedding') {
                    $delay = displayProjects($con, 'wedding', 'wedding', 'book_wed.php', $delay);
                }
                if($category == 'all' || $category == 'birthday') {
                    $delay = displayProjects($con, 'birthday', 'birthday', 'book_bday.php', $delay);
                }
                if($category == 'all' || $category == 'anniversary') {
                    $delay = displayProjects($con, 'anniversary', 'anniversary', 'book_anni.php', $delay);
                }
                if($category == 'all' || $category == 'corporate') {
                    $delay = displayProjects($con, 'otherevent', 'corporate', 'book_other.php', $delay);
                }
                ?>
            </div>

            <!-- View More Button -->
            <div class="text-center mt-5" data-aos="fade-up">
                <a href="gallery.php" class="btn-view">
                    <i class="fas fa-images me-2"></i>View Complete Gallery
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="100">
                    <span class="stat-number">150+</span>
                    <p class="stat-label">Projects Completed</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="200">
                    <span class="stat-number">98%</span>
                    <p class="stat-label">Client Satisfaction</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="300">
                    <span class="stat-number">24/7</span>
                    <p class="stat-label">Support Available</p>
                </div>
                <div class="col-md-3 stat-item" data-aos="fade-up" data-aos-delay="400">
                    <span class="stat-number">5+</span>
                    <p class="stat-label">Years Experience</p>
                </div>
            </div>
        </div>
    </section>


    <!-- Footer -->
    <?php include_once("footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Lightbox configuration
        lightbox.option({
            'resizeDuration': 200,
            'wrapAround': true,
            'albumLabel': "Image %1 of %2"
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
    <style>
    .footer, .footer * {
        color: #b39ddb !important;
    }
    .footer .text-danger {
        color: #f5576c !important;
    }
    .footer .btn-footer {
        color: #fff !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none;
        font-weight: 600;
        font-size: 1.1rem;
    }
    .footer .btn-footer:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
        color: #fff !important;
    }
    </style>
</body>
</html>


