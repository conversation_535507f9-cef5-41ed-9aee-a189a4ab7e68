<?php
// Start session before any output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FirmAnt Event Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark: #1a1a2e;
            --light: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
        }

        /* Animated Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 2px 30px rgba(0,0,0,0.15);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Hero Section with Carousel */
        .hero-carousel {
            height: 100vh;
            position: relative;
        }

        .carousel-item {
            height: 100vh;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .carousel-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.6));
        }

        .hero-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 2;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.3s both;
        }

        .hero-buttons {
            animation: fadeInUp 1s ease 0.6s both;
        }

        .btn-hero {
            padding: 15px 40px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-hero {
            background: var(--secondary);
            border: none;
            color: white;
        }

        .btn-primary-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
        }

        .btn-outline-hero {
            border: 2px solid white;
            color: white;
            background: transparent;
        }

        .btn-outline-hero:hover {
            background: white;
            color: #333;
            transform: translateY(-3px);
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        /* Services Section */
        .services-section {
            padding: 100px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary);
            transition: all 0.5s ease;
            z-index: 1;
        }

        .service-card:hover::before {
            left: 0;
        }

        .service-card:hover {
            transform: translateY(-10px);
            color: white;
        }

        .service-card * {
            position: relative;
            z-index: 2;
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-icon {
            -webkit-text-fill-color: white;
        }

        /* Stats Section */
        .stats-section {
            background: var(--dark);
            color: white;
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }


        /* Testimonials Carousel - Redesigned */
        .testimonials-section {
            padding: 100px 0;
            background: #f7fafd;
        }

        .testimonial-card {
            background: #fff;
            border-radius: 18px;
            padding: 48px 36px 36px 36px;
            box-shadow: 0 8px 32px rgba(60, 72, 88, 0.12);
            text-align: center;
            margin: 24px 0;
            border: 1px solid #e3e8ee;
            transition: box-shadow 0.3s;
        }
        .testimonial-card:hover {
            box-shadow: 0 16px 48px rgba(60, 72, 88, 0.18);
        }

        .testimonial-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: #e3e8ee;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: #667eea;
        }
        .testimonial-card h5 {
            font-weight: 700;
            color: #222;
            margin-bottom: 0.25rem;
        }
        .testimonial-card p.text-muted {
            color: #7b809a !important;
            margin-bottom: 1.2rem;
        }
        .testimonial-card p.lead {
            color: #444;
            font-size: 1.1rem;
            margin-bottom: 1.2rem;
        }
        .testimonial-card .text-warning i {
            color: #f7b731;
            font-size: 1.2rem;
        }
        @media (max-width: 768px) {
            .testimonial-card {
                padding: 28px 10px 24px 10px;
            }
        }


        /* Footer */
        .footer {
            background: var(--dark);
            color: #f1f1f1;
            padding: 60px 0 20px;
        }

        .footer-brand {
            font-size: 2rem;
            font-weight: 800;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-text, .footer .footer-text, .footer .footer-link, .footer .footer-link:visited {
            color: #e0e0e0 !important;
        }

        .footer-link:hover {
            color: #fff !important;
            text-decoration: underline;
        }

        .social-links a {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 50px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Navigation Spacing */
        .navbar-nav.mx-auto {
            margin-left: 3rem !important;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title { font-size: 2.5rem; }
            .hero-subtitle { font-size: 1.1rem; }
            .btn-hero { padding: 12px 30px; }

            /* Reset navigation spacing on mobile */
            .navbar-nav.mx-auto {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;max-width:160px;vertical-align:middle;object-fit:contain;border:none;box-shadow:none;background:none;"> 
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Items - Centered with left margin -->
                <ul class="navbar-nav mx-auto align-items-center" style="margin-left: 3rem;">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="projects.php">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                </ul>

                <!-- User Actions Group - Right aligned -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item me-3">
                        <?php
                        include_once("cart_functions.php");
                        $cart_count = $cartManager->getCartCount();
                        ?>
                        <a class="nav-link position-relative" href="cart.php" title="View Cart">
                            <i class="fas fa-shopping-cart fa-lg"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;"> <?php echo $cart_count; ?> </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['uid'])): ?>
                        <li class="nav-item me-3">
                            <a class="nav-link d-flex align-items-center" href="my_orders.php">
                                <i class="fas fa-receipt fa-lg me-1"></i> My Orders
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <?php if (isset($_SESSION['uid'])): ?>
                            <a class="nav-link d-flex align-items-center text-danger" href="logout.php?redirect=login">
                                <i class="fas fa-sign-out-alt fa-lg me-1"></i> Sign Out
                            </a>
                        <?php else: ?>
                            <a class="nav-link d-flex align-items-center" href="login.php">
                                <i class="fas fa-sign-in-alt fa-lg me-1"></i> Login
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Carousel -->
    <section id="home" class="hero-carousel">
        <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-inner">
                <div class="carousel-item active" style="background-image: url('images/cs_wedding1.jpg')">
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                    <div class="hero-content">
                        <h1 class="hero-title">Create Magical <span style="background: var(--secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Weddings</span></h1>
                        <p class="hero-subtitle">Transform your dream wedding into reality with our premium planning services</p>
                        <div class="hero-buttons">
                            <a href="book.php" class="btn btn-hero btn-primary-hero me-3">
                                <i class="fas fa-calendar-plus me-2"></i>Book Now
                            </a>
                            <a href="gallery.php" class="btn btn-hero btn-outline-hero">
                                <i class="fas fa-images me-2"></i>View Gallery
                            </a>
                        </div>
                    </div>
                </div>
                <div class="carousel-item" style="background-image: url('images/cs_birthday1.jpg')">
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                    <div class="hero-content">
                        <h1 class="hero-title">Celebrate <span style="background: var(--accent); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Birthdays</span></h1>
                        <p class="hero-subtitle">Make every birthday unforgettable with creative themes and decorations</p>
                        <div class="hero-buttons">
                            <a href="book.php" class="btn btn-hero btn-primary-hero me-3">
                                <i class="fas fa-gift me-2"></i>Plan Party
                            </a>
                            <a href="birthday.php" class="btn btn-hero btn-outline-hero">
                                <i class="fas fa-birthday-cake me-2"></i>See Ideas
                            </a>
                        </div>
                    </div>
                </div>
                <div class="carousel-item" style="background-image: url('images/cs_corporat.jpg')">
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                    <div class="hero-content">
                        <h1 class="hero-title">Professional <span style="background: var(--primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Events</span></h1>
                        <p class="hero-subtitle">Elevate your corporate events with sophisticated planning and execution</p>
                        <div class="hero-buttons">
                            <a href="book.php" class="btn btn-hero btn-primary-hero me-3">
                                <i class="fas fa-briefcase me-2"></i>Get Quote
                            </a>
                            <a href="other_events.php" class="btn btn-hero btn-outline-hero">
                                <i class="fas fa-building me-2"></i>Learn More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
            </button>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-4 fw-bold mb-3">Our Premium Services</h2>
                <p class="lead text-muted">Creating extraordinary experiences for every occasion</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h4 class="mb-3">Weddings</h4>
                        <p class="mb-4">Complete wedding planning with mandap decoration, floral arrangements, and photography</p>
                        <a href="wedding.php" class="btn btn-outline-primary rounded-pill">Explore</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h4 class="mb-3">Birthday Parties</h4>
                        <p class="mb-4">Creative birthday themes, balloon decorations, and entertainment for all ages</p>
                        <a href="birthday.php" class="btn btn-outline-primary rounded-pill">Explore</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h4 class="mb-3">Anniversaries</h4>
                        <p class="mb-4">Romantic anniversary celebrations with elegant decorations and intimate settings</p>
                        <a href="anniversary.php" class="btn btn-outline-primary rounded-pill">Explore</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h4 class="mb-3">Corporate Events</h4>
                        <p class="mb-4">Professional corporate events, conferences, and team building activities</p>
                        <a href="other_events.php" class="btn btn-outline-primary rounded-pill">Explore</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-item">
                        <div class="stat-number" data-count="500">0</div>
                        <h5>Events Completed</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item">
                        <div class="stat-number" data-count="50">0</div>
                        <h5>Unique Themes</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-item">
                        <div class="stat-number" data-count="98">0</div>
                        <h5>Client Satisfaction</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-item">
                        <div class="stat-number" data-count="5">0</div>
                        <h5>Years Experience</h5>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-4 fw-bold mb-3">What Our Clients Say</h2>
                <p class="lead text-muted">Real experiences from real people</p>
            </div>
            <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="row justify-content-center">
                            <div class="col-lg-8">
                                <div class="testimonial-card">
                                    <div class="testimonial-avatar"></div>
                                    <h5>Sarah Johnson</h5>
                                    <p class="text-muted mb-3">Wedding Client</p>
                                    <p class="lead">"FirmAnt made our wedding absolutely magical! Every detail was perfect and the team was incredibly professional."</p>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="row justify-content-center">
                            <div class="col-lg-8">
                                <div class="testimonial-card">
                                    <div class="testimonial-avatar"></div>
                                    <h5>Michael Chen</h5>
                                    <p class="text-muted mb-3">Corporate Client</p>
                                    <p class="lead">"Outstanding service for our company's annual event. The attention to detail and professionalism exceeded our expectations."</p>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="footer-brand mb-3">
                        <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;max-width:160px;vertical-align:middle;object-fit:contain;border:none;box-shadow:none;background:none;"> 
                    </div>
                    <p class="footer-text">Creating unforgettable moments and extraordinary experiences for every special occasion.</p>
                    <div class="social-links mt-4">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Services</h5>
                    <ul class="list-unstyled">
                        <li><a href="wedding.php" class="footer-link">Weddings</a></li>
                        <li><a href="birthday.php" class="footer-link">Birthdays</a></li>
                        <li><a href="anniversary.php" class="footer-link">Anniversaries</a></li>
                        <li><a href="other_events.php" class="footer-link">Corporate</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Company</h5>
                    <ul class="list-unstyled">
                        <li><a href="gallery.php" class="footer-link">Gallery</a></li>
                        <li><a href="projects.php" class="footer-link">Projects</a></li>
                        <li><a href="contact.php" class="footer-link">Contact</a></li>
                        <li><a href="book.php" class="footer-link">Book Event</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">Contact Info</h5>
                    <p class="footer-text"><i class="fas fa-map-marker-alt me-2"></i>123 Event Street, City, State 12345</p>
                    <p class="footer-text"><i class="fas fa-phone me-2"></i>+****************</p>
                    <p class="footer-text"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="footer-text mb-0">&copy; <?php echo date('Y'); ?> FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target + (target === 98 ? '%' : '+');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current) + (target === 98 ? '%' : '+');
                    }
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(document.querySelector('.stats-section'));

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>





