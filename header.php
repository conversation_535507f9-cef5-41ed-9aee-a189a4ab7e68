<!--
Author: <PERSON><PERSON><PERSON><PERSON> EVENTS
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="images/logo.png">
    <title>FirmAnt Event Management System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark: #1a1a2e;
            --light: #ffffff;
        }

        body {
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 2px 30px rgba(0,0,0,0.15);
            padding: 0.5rem 0;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
        }

        .navbar-brand:hover {
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: var(--dark) !important;
            position: relative;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 0.5rem 0;
        }

        .dropdown-item {
            padding: 0.7rem 1.5rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-auth {
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .btn-primary-auth {
            background: var(--primary);
            color: white;
            border: 2px solid transparent;
        }

        .btn-outline-auth {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .contact-info {
            font-size: 0.9rem;
            color: #666;
        }

        .contact-info i {
            margin-right: 0.5rem;
            color: #667eea;
        }

        @media (max-width: 991px) {
            .contact-info {
                text-align: center;
                margin-bottom: 1rem;
            }
            
            .auth-buttons {
                text-align: center;
                margin-top: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Modern Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:48px;vertical-align:middle;"> <span class="visually-hidden">FirmAnt</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Contact Info -->
                <div class="contact-info d-lg-flex d-none ms-auto me-4">
                    <span class="me-3">
                        <i class="fas fa-phone"></i>
                        <a href="tel:+237651734631" style="color: inherit; text-decoration: none;">+237 651 734 631</a>
                    </span>
                    <span>
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>" style="color: inherit; text-decoration: none;"><EMAIL></a>
                    </span>
                </div>
                
                <!-- Navigation Links -->
                <ul class="navbar-nav me-auto d-flex align-items-center" style="gap: 1.5rem; margin-left: 1rem;">
                    <li class="nav-item" style="margin-right: 0.5rem;">
                        <a class="nav-link px-2" href="index.php">Home</a>
                    </li>
                    <li class="nav-item" style="margin-right: 0.5rem;">
                        <a class="nav-link px-2" href="about.php">About</a>
                    </li>
                    <li class="nav-item dropdown" style="margin-right: 0.5rem;">
                        <a class="nav-link dropdown-toggle px-2" href="#" role="button" data-bs-toggle="dropdown">
                            Services
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="wedding.php">Weddings</a></li>
                            <li><a class="dropdown-item" href="birthday.php">Birthday Parties</a></li>
                            <li><a class="dropdown-item" href="anniversary.php">Anniversaries</a></li>
                            <li><a class="dropdown-item" href="other_events.php">Other Events</a></li>
                        </ul>
                    </li>
                    <li class="nav-item" style="margin-right: 0.5rem;">
                        <a class="nav-link px-2" href="gallery.php">Gallery</a>
                    </li>
                    <li class="nav-item" style="margin-right: 0.5rem;">
                        <a class="nav-link px-2" href="contact.php">Contact</a>
                    </li>
                </ul>
                
                <!-- Auth Buttons -->
                <div class="auth-buttons">
                    <?php
                    @session_start();
                    if(isset($_SESSION['uid'])) {
                        echo '<a href="logout.php?redirect=login" class="btn btn-auth btn-danger">Sign Out</a>';
                    } else {
                        echo '<a href="registration.php" class="btn btn-auth btn-outline-auth me-2">Sign Up</a>';
                        echo '<a href="login.php" class="btn btn-auth btn-primary-auth">Login</a>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Navbar Scroll Effect Script -->
    <script>
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>


