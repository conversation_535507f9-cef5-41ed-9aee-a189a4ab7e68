<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Redirect to login if not logged in
if (!isset($_SESSION['uid'])) {
    header('Location: login.php?redirect=checkout');
    exit();
}

include_once("cart_functions.php");

// Get cart items
$user_cart_items = $cartManager->getUserCartItems($_SESSION['uid']);
if (empty($user_cart_items)) {
    header('Location: cart.php?error=empty_cart');
    exit();
}

// Calculate totals
$subtotal = 0;
foreach ($user_cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}
$tax_rate = 0.10; // 10% tax
$tax_amount = $subtotal * $tax_rate;
$total = $subtotal + $tax_amount;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['place_order'])) {
    $shipping_address = $_POST['shipping_address'];
    $billing_address = $_POST['billing_address'];
    $notes = $_POST['notes'] ?? '';
    
    // Generate order number
    $order_number = 'ORD-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // Insert order
    $order_query = "INSERT INTO orders (user_id, order_number, total_amount, tax_amount, final_amount, shipping_address, billing_address, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $order_stmt = mysqli_prepare($con, $order_query);
    mysqli_stmt_bind_param($order_stmt, "isdddsss", $_SESSION['uid'], $order_number, $subtotal, $tax_amount, $total, $shipping_address, $billing_address, $notes);
    
    if (mysqli_stmt_execute($order_stmt)) {
        $order_id = mysqli_insert_id($con);
        
        // Insert order items
        $item_query = "INSERT INTO order_items (order_id, item_id, item_type, item_name, item_image, quantity, unit_price, total_price) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $item_stmt = mysqli_prepare($con, $item_query);
        
        foreach ($user_cart_items as $item) {
            $item_total = $item['price'] * $item['quantity'];
            mysqli_stmt_bind_param($item_stmt, "iisssidd", $order_id, $item['item_id'], $item['item_type'], 
                                 $item['item_name'], $item['item_image'], $item['quantity'], $item['price'], $item_total);
            mysqli_stmt_execute($item_stmt);
        }
        
        // Clear cart
        $cartManager->clearCart();
        
        // Redirect to success page
        header('Location: order_success.php?order=' . $order_number);
        exit();
    } else {
        $error_message = "Failed to place order. Please try again.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout | FirmAnt Event Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --dark: #1a1a2e;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .checkout-hero {
            background: var(--primary);
            color: white;
            padding: 120px 0 60px;
            text-align: center;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .checkout-section {
            padding: 60px 0;
        }

        .checkout-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .order-summary {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            position: sticky;
            top: 100px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .summary-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .place-order-btn {
            background: var(--success);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .place-order-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(67, 233, 123, 0.4);
            color: white;
        }

        .order-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 15px;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-price {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="images/real-logo.png" alt="FirmAnt Logo" style="height:40px;vertical-align:middle;"> 
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="cart.php">
                    <i class="fas fa-arrow-left me-2"></i>Back to Cart
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="checkout-hero">
        <div class="container">
            <h1 class="hero-title">Checkout</h1>
            <p class="lead">Complete your order and bring your event to life</p>
        </div>
    </section>

    <!-- Checkout Section -->
    <section class="checkout-section">
        <div class="container">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Shipping Information -->
                        <div class="checkout-card">
                            <h4 class="mb-4">Shipping Information</h4>
                            <div class="form-group mb-3">
                                <label class="form-label">Shipping Address</label>
                                <textarea class="form-control" name="shipping_address" rows="4" required 
                                          placeholder="Enter your complete shipping address"></textarea>
                            </div>
                        </div>

                        <!-- Billing Information -->
                        <div class="checkout-card">
                            <h4 class="mb-4">Billing Information</h4>
                            <div class="form-group mb-3">
                                <label class="form-label">Billing Address</label>
                                <textarea class="form-control" name="billing_address" rows="4" required 
                                          placeholder="Enter your billing address (same as shipping if identical)"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Additional Notes (Optional)</label>
                                <textarea class="form-control" name="notes" rows="3" 
                                          placeholder="Any special instructions or requirements"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <h4 class="mb-4">Order Summary</h4>
                            
                            <!-- Order Items -->
                            <div class="order-items mb-4">
                                <?php foreach ($user_cart_items as $item): ?>
                                    <div class="order-item">
                                        <img src="images/<?php echo $item['item_image']; ?>" alt="<?php echo $item['item_name']; ?>" class="item-image">
                                        <div class="item-details">
                                            <div class="item-name"><?php echo $item['item_name']; ?></div>
                                            <div class="item-price">Qty: <?php echo $item['quantity']; ?> × $<?php echo number_format($item['price'], 2); ?></div>
                                        </div>
                                        <div class="item-total">
                                            <strong>$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></strong>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Summary Totals -->
                            <div class="summary-item">
                                <span>Subtotal</span>
                                <span>$<?php echo number_format($subtotal, 2); ?></span>
                            </div>
                            <div class="summary-item">
                                <span>Tax (10%)</span>
                                <span>$<?php echo number_format($tax_amount, 2); ?></span>
                            </div>
                            <div class="summary-item">
                                <span>Total</span>
                                <span>$<?php echo number_format($total, 2); ?></span>
                            </div>

                            <button type="submit" name="place_order" class="place-order-btn">
                                <i class="fas fa-credit-card me-2"></i>Place Order
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
