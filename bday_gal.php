<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Gallery | FirmAnt Event Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
    <style>
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            overflow-x: hidden;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-link {
            font-weight: 500;
            color: #333 !important;
            transition: all 0.3s ease;
            position: relative;
            margin: 0 10px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .gallery-hero {
            background: var(--secondary);
            min-height: 70vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .gallery-filters {
            padding: 60px 0;
            background: white;
        }

        .filter-btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: white;
            color: #333;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--secondary);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
        }

        .gallery-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .gallery-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            margin-bottom: 30px;
        }

        .gallery-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.2);
        }

        .gallery-image {
            position: relative;
            overflow: hidden;
        }

        .gallery-card img {
            width: 100%;
            height: 280px;
            object-fit: cover;
            transition: all 0.4s ease;
        }

        .gallery-card:hover img {
            transform: scale(1.1);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(240, 147, 251, 0.9), rgba(245, 87, 108, 0.9));
            opacity: 0;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .gallery-card:hover .image-overlay {
            opacity: 1;
        }

        .overlay-content {
            text-align: center;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .gallery-card:hover .overlay-content {
            transform: translateY(0);
        }

        .btn-book {
            background: var(--secondary);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
        }

        .btn-book:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
            color: white;
        }

        .footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 30px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-star me-2"></i>FirmAnt
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="gallery.php">Gallery</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">Contact</a></li>
                    <li class="nav-item"><a class="nav-link" href="login.php">Login</a></li>
                    <li class="nav-item"><a class="nav-link" href="registration.php">Register</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Gallery Hero Section -->
    <section class="gallery-hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Birthday Gallery</h1>
                <p class="hero-subtitle">Discover amazing birthday party themes and decorations</p>
            </div>
        </div>
    </section>

    <!-- Gallery Filters -->
    <section class="gallery-filters">
        <div class="container">
            <div class="text-center">
                <h2 class="display-5 fw-bold mb-4">Event Categories</h2>
                <div class="filter-buttons">
                    <a href="gallery.php" class="filter-btn">Wedding</a>
                    <a href="bday_gal.php" class="filter-btn active">Birthday Party</a>
                    <a href="anni_gal.php" class="filter-btn">Anniversary</a>
                    <a href="other_gal.php" class="filter-btn">Entertainment</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <div class="row g-4">
                <?php
                    include_once("Database/connect.php");
                    $qry = "SELECT * FROM birthday ORDER BY id DESC";
                    $res = mysqli_query($con, $qry) or die("Can't fetch data");
                    $delay = 100;
                    while($row = mysqli_fetch_array($res)){
                ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                    <div class="gallery-card">
                        <div class="gallery-image">
                            <a href="images/<?php echo $row['img']; ?>" data-lightbox="birthday-gallery" data-title="<?php echo isset($row['nm']) ? $row['nm'] : 'Birthday Event'; ?>">
                                <img src="images/<?php echo $row['img']; ?>" alt="Birthday Event">
                                <div class="image-overlay">
                                    <div class="overlay-content">
                                        <i class="fas fa-search-plus fa-3x mb-3"></i>
                                        <h5><?php echo isset($row['nm']) ? $row['nm'] : 'Birthday Event'; ?></h5>
                                        <p class="mb-3">Price: $<?php echo $row['price']; ?></p>
                                        <a href="book_bday.php?id=<?php echo $row['id']; ?>" class="btn-book">Book Now</a>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <?php 
                    $delay += 100;
                    } 
                ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <h4 class="mb-3"><i class="fas fa-calendar-star me-2"></i>FirmAnt Events</h4>
                <p class="text-muted">&copy; 2024 FirmAnt Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::after {
            color: var(--accent);
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a::after {
            transition: color 0.3s ease;
        }

        .gallery-section .gallery-grid .gallery-item .gallery-overlay .gallery-caption .gallery-description .gallery-link a:hover::before,
        .gallery-section .gallery-grid .gallery-item .gallery-overlay .


